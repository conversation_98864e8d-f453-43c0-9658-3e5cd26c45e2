<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录</title>
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-hover: #1d4ed8;
            --text-color: #334155;
            --text-muted: #64748b;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --border-color: #e2e8f0;
            --success-color: #059669;
            --success-light: #d1fae5;
            --danger-color: #dc2626;
            --danger-light: #fee2e2;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --transition: all 0.2s ease-in-out;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-attachment: fixed;
            min-height: 100vh;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
            color: var(--text-color);
            overflow: hidden;
            position: relative;
        }

        /* 炫酷背景动画 */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
            animation: backgroundMove 20s ease-in-out infinite;
            z-index: -1;
        }

        @keyframes backgroundMove {
            0%, 100% { transform: translateX(0) translateY(0) rotate(0deg); }
            33% { transform: translateX(-30px) translateY(-50px) rotate(120deg); }
            66% { transform: translateX(20px) translateY(30px) rotate(240deg); }
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 40px;
            border-radius: 20px;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
            text-align: center;
            max-width: 420px;
            width: 100%;
            border: 1px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            animation: shimmer 3s infinite;
            z-index: 0;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* Logo样式 */
        .logo-container {
            margin-bottom: 25px;
            position: relative;
            z-index: 1;
            text-align: center;
        }

        .logo-container img {
            max-width: 150px;
            max-height: 100px;
            object-fit: contain;
            border-radius: 12px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255, 255, 255, 0.9);
            padding: 8px;
        }

        .logo-container img:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        h1 {
            color: #333;
            margin-bottom: 30px;
            font-weight: 700;
            font-size: 1.8rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            z-index: 1;
        }

        input {
            width: 100%;
            padding: 14px 18px;
            margin: 12px 0;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.8);
            color: #333;
            font-size: 15px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-sizing: border-box;
            backdrop-filter: blur(10px);
            position: relative;
            z-index: 1;
        }

        input:focus {
            outline: none;
            border-color: #667eea;
            background: rgba(255, 255, 255, 0.95);
            box-shadow:
                0 0 0 4px rgba(102, 126, 234, 0.1),
                0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        input::placeholder {
            color: rgba(51, 51, 51, 0.6);
        }

        button {
            width: 100%;
            padding: 16px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin-top: 20px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            z-index: 1;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }

        button:hover::before {
            left: 100%;
        }

        button:active {
            transform: translateY(0);
        }

        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }



        .alert {
            padding: 12px 16px;
            margin: 16px 0;
            border-radius: var(--border-radius);
            font-size: 14px;
        }

        .alert-danger {
            background: var(--danger-light);
            color: var(--danger-color);
            border: 1px solid rgba(220, 38, 38, 0.2);
        }

        .alert-success {
            background: var(--success-light);
            color: var(--success-color);
            border: 1px solid rgba(5, 150, 105, 0.2);
        }

        /* 移动端优化 - 防止滚动条 */
        @media (max-width: 576px) {
            html, body {
                overflow: hidden !important;
                max-width: 100vw !important;
                height: 100vh !important;
            }

            body {
                padding: 15px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }

            .container {
                padding: 30px 20px !important;
                margin: 0 !important;
                max-width: 100% !important;
                width: 100% !important;
                max-height: 90vh !important;
                overflow-y: auto !important;
                box-sizing: border-box !important;
            }

            .logo-container img {
                max-width: 100px !important;
                max-height: 70px !important;
                padding: 6px !important;
            }

            h1 {
                font-size: 1.4rem !important;
                margin-bottom: 20px !important;
            }

            input {
                padding: 12px 16px !important;
                font-size: 14px !important;
                margin: 10px 0 !important;
            }

            button {
                padding: 14px 18px !important;
                font-size: 15px !important;
                margin-top: 15px !important;
            }


        }
    </style>
</head>
<body>
    <div class="container">
        <?php if (!empty($settings['site_logo'])): ?>
        <div class="logo-container">
            <img src="<?php echo htmlspecialchars($settings['site_logo']); ?>" alt="<?php echo htmlspecialchars($settings['site_name'] ?? '网站Logo'); ?>" onerror="this.style.display='none'">
        </div>
        <?php endif; ?>
        <h1>管理员登录</h1>

        <form id="loginForm">
            <div id="alertArea"></div>
            <?php if (session('timeout_msg')): ?>
            <div class="alert alert-danger">
                <?= session('timeout_msg') ?>
            </div>
            <?php endif; ?>

            <input type="text" id="username" name="username" placeholder="用户名" required>
            <input type="password" id="password" name="password" placeholder="密码" required>

            <button type="submit" id="loginBtn">登录</button>
        </form>
        

    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            const loginBtn = document.getElementById('loginBtn');

            if (!username || !password) {
                showAlert('请输入用户名和密码', 'danger');
                return;
            }

            loginBtn.innerHTML = '登录中...';
            loginBtn.disabled = true;

            // 使用XMLHttpRequest确保兼容性
            const xhr = new XMLHttpRequest();
            xhr.open('POST', '/admin/login', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    loginBtn.innerHTML = '登录';
                    loginBtn.disabled = false;

                    if (xhr.status === 200) {
                        try {
                            const data = JSON.parse(xhr.responseText);
                            if (data.code === 1) {
                                showAlert('登录成功，正在跳转...', 'success');
                                // 使用多种方式确保跳转成功
                                const redirectUrl = data.url || '/admin/dashboard';
                                setTimeout(() => {
                                    window.location.replace(redirectUrl);
                                }, 100);
                                // 备用跳转方式
                                setTimeout(() => {
                                    if (window.location.pathname === '/admin/login') {
                                        window.location.href = redirectUrl;
                                    }
                                }, 1000);
                            } else {
                                showAlert(data.msg || '登录失败', 'danger');
                            }
                        } catch (e) {
                            showAlert('服务器响应错误', 'danger');
                        }
                    } else {
                        showAlert('网络错误，请稍后重试', 'danger');
                    }
                }
            };

            xhr.send('username=' + encodeURIComponent(username) + '&password=' + encodeURIComponent(password));
        });
        
        function showAlert(message, type) {
            const alertArea = document.getElementById('alertArea');
            alertArea.innerHTML = '<div class="alert alert-' + type + '">' + message + '</div>';
        }
    </script>
</body>
</html>
