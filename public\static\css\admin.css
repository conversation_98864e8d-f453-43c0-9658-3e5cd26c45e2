/* 管理后端样式 - 清爽简洁设计 */

:root {
    /* 主色调 - 清爽蓝色系 */
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --primary-light: #dbeafe;

    /* 中性色 */
    --secondary-color: #64748b;
    --text-color: #334155;
    --text-muted: #64748b;
    --text-light: #94a3b8;

    /* 功能色 - 柔和版本 */
    --success-color: #059669;
    --success-light: #d1fae5;
    --warning-color: #d97706;
    --warning-light: #fef3c7;
    --danger-color: #dc2626;
    --danger-light: #fee2e2;
    --info-color: #0891b2;
    --info-light: #cffafe;

    /* 背景色 */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --border-color: #e2e8f0;
    --border-light: #f1f5f9;

    /* 布局 */
    --sidebar-width: 260px;
    --sidebar-collapsed-width: 70px;
    --header-height: 64px;
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --box-shadow-lg: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --transition: all 0.2s ease-in-out;
}

/* 全局样式重置 */
* {
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--bg-secondary);
    margin: 0;
    padding: 0;
    font-size: 14px;
    font-weight: 400;
}

/* 主布局 */
.admin-wrapper {
    display: flex;
    min-height: 100vh;
    background-color: var(--bg-secondary);
}

/* 侧边栏 */
.sidebar {
    width: var(--sidebar-width);
    background: var(--bg-primary);
    border-right: 1px solid var(--border-color);
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    transition: var(--transition);
    box-shadow: var(--box-shadow);
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-brand {
    color: var(--text-color);
    font-size: 1.125rem;
    font-weight: 600;
    text-decoration: none;
    display: flex;
    align-items: center;
    transition: var(--transition);
}

.sidebar-brand:hover {
    color: var(--primary-color);
    text-decoration: none;
}

.sidebar-brand i {
    margin-right: 10px;
    font-size: 1.25rem;
    color: var(--primary-color);
}

.brand-text {
    white-space: nowrap;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--text-light);
    font-size: 1.125rem;
    cursor: pointer;
    padding: 6px;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.sidebar-toggle:hover {
    color: var(--text-color);
    background: var(--bg-tertiary);
}

/* 导航菜单 */
.sidebar-menu {
    padding: 16px 0;
}

.sidebar-menu .nav {
    padding: 0;
    margin: 0;
    list-style: none;
}

.nav-item {
    margin-bottom: 2px;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--text-muted);
    text-decoration: none;
    transition: var(--transition);
    position: relative;
    border-radius: var(--border-radius);
    margin: 0 12px;
    font-weight: 500;
    font-size: 14px;
}

.nav-link:hover {
    background: var(--bg-tertiary);
    color: var(--text-color);
    text-decoration: none;
}

.nav-link.active {
    background: var(--primary-light);
    color: var(--primary-color);
    font-weight: 600;
}

.nav-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 20px;
    background: var(--primary-color);
    border-radius: 0 2px 2px 0;
}

/* 禁用dropdown-toggle菜单项的左侧装饰线 */
.nav-link.dropdown-toggle.active::before {
    display: none;
}

.nav-link i {
    margin-right: 10px;
    width: 18px;
    text-align: center;
    font-size: 16px;
}

.nav-link span {
    white-space: nowrap;
}

/* 子菜单样式 */
.nav-link.dropdown-toggle::after {
    content: '\f105'; /* 使用右箭头图标 */
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-left: auto;
    transition: transform 0.2s ease;
    font-size: 12px;
    line-height: 1; /* 修复图标上方空白问题 */
    display: inline-block;
    vertical-align: middle;
}

.nav-link.dropdown-toggle[aria-expanded="true"]::after {
    transform: rotate(90deg); /* 展开时向下旋转90度 */
}

.nav-link.dropdown-toggle {
    justify-content: space-between;
    align-items: center; /* 确保垂直居中对齐 */
}

/* 子菜单容器 - 参照图片样式 */
.sidebar-menu .collapse {
    transition: none;
}

.submenu-list {
    background: transparent;
    margin: 0;
    padding: 0;
}

.submenu-item {
    padding: 8px 20px 8px 45px; /* 左侧增加缩进 */
    margin: 0 12px; /* 与主菜单保持一致的左右边距 */
    border-radius: var(--border-radius); /* 使用与主菜单相同的圆角 */
    font-size: 13px;
    color: var(--text-muted);
    background: transparent;
    transition: background-color 0.15s ease, color 0.15s ease;
    position: relative;
}

.submenu-item:hover {
    background: var(--bg-tertiary); /* 使用与主菜单相同的悬停背景 */
    color: var(--text-color);
}

.submenu-item.active {
    background: var(--primary-light);
    color: var(--primary-color);
    font-weight: 500;
}

.submenu-item.active::before {
    display: none;
}

/* 子菜单图标样式 */
.submenu-icon {
    margin-right: 10px;
    width: 14px;
    font-size: 13px;
    color: var(--text-muted);
    text-align: center;
    display: inline-block;
}

.submenu-item:hover .submenu-icon {
    color: var(--primary-color);
}

.submenu-item.active .submenu-icon {
    color: var(--primary-color);
}

/* 主内容区 */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* 顶部导航 */
.top-header {
    background: var(--bg-primary);
    padding: 0 24px;
    height: var(--header-height);
    box-shadow: var(--box-shadow);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 12px;
}

.page-title {
    font-size: 1.375rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
    letter-spacing: -0.025em;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* 用户信息显示 - 简洁一行样式 */
.user-info-display {
    padding: 8px 16px;
    background: transparent;
    border-radius: var(--border-radius);
}

/* 用户头像 - 简洁样式 */
.user-avatar-simple {
    border-radius: 50%;
    object-fit: cover;
    border: 1px solid var(--border-color);
}

/* 用户详情 */
.user-details {
    display: flex;
    align-items: center;
    font-size: 14px;
    line-height: 1;
}

.user-name-simple {
    font-weight: 600;
    color: var(--text-color);
}

.user-separator {
    margin: 0 8px;
    color: var(--text-muted);
    font-weight: 300;
}

.user-role-simple {
    font-size: 13px;
    color: var(--text-muted);
    font-weight: 500;
}

/* 用户操作链接 */
.user-action-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: var(--border-radius);
    color: var(--text-muted);
    text-decoration: none;
    transition: var(--transition);
    font-size: 14px;
}

.user-action-link:hover {
    background-color: var(--bg-tertiary);
    color: var(--primary-color);
    text-decoration: none;
}

.user-action-link.text-danger:hover {
    background-color: var(--danger-light);
    color: var(--danger-color);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .user-details {
        display: none;
    }

    .user-info-display {
        padding: 8px 12px;
    }

    .user-action-link {
        width: 28px;
        height: 28px;
        font-size: 13px;
    }
}

/* 内容区域 */
.content-wrapper {
    flex: 1;
    padding: 24px;
    background-color: var(--bg-secondary);
    overflow-y: auto;
}

/* 统计卡片 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
}

.stat-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    padding: 24px;
    box-shadow: var(--box-shadow);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-color);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

.stat-card.primary::before { background: var(--primary-color); }
.stat-card.success::before { background: var(--success-color); }
.stat-card.warning::before { background: var(--warning-color); }
.stat-card.danger::before { background: var(--danger-color); }
.stat-card.info::before { background: var(--info-color); }

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.stat-title {
    color: var(--text-muted);
    font-size: 0.875rem;
    font-weight: 500;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 1.125rem;
}

.stat-icon.primary { background: var(--primary-color); }
.stat-icon.success { background: var(--success-color); }
.stat-icon.warning { background: var(--warning-color); }
.stat-icon.danger { background: var(--danger-color); }
.stat-icon.info { background: var(--info-color); }

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-color);
    margin: 0;
    line-height: 1.2;
}

.stat-change {
    font-size: 0.8125rem;
    margin-top: 8px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--danger-color);
}

.stat-change i {
    font-size: 0.75rem;
}

/* 图表容器 */
.chart-container {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    padding: 24px;
    box-shadow: var(--box-shadow);
    border: 1px solid var(--border-color);
    margin-bottom: 24px;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border-light);
}

.chart-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
    letter-spacing: -0.025em;
}

.chart-controls {
    display: flex;
    gap: 6px;
}

.chart-controls .btn {
    padding: 6px 12px;
    font-size: 0.8125rem;
    border-radius: var(--border-radius);
    font-weight: 500;
}

/* 表格样式 */
.table-container {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    border: 1px solid var(--border-color);
}

.table {
    width: 100%;
    margin: 0;
    border-collapse: collapse;
}

.table th {
    background: var(--bg-tertiary);
    padding: 12px 16px;
    font-weight: 600;
    color: var(--text-color);
    border-bottom: 1px solid var(--border-color);
    text-align: left;
    font-size: 0.8125rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.table td {
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-light);
    color: var(--text-muted);
    vertical-align: middle;
    font-size: 0.875rem;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: var(--bg-secondary);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* 状态标签 */
.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.status-badge.success {
    background: var(--success-light);
    color: var(--success-color);
}

.status-badge.warning {
    background: var(--warning-light);
    color: var(--warning-color);
}

.status-badge.danger {
    background: var(--danger-light);
    color: var(--danger-color);
}

.status-badge.info {
    background: var(--info-light);
    color: var(--info-color);
}

.status-badge.secondary {
    background: var(--bg-tertiary);
    color: var(--text-muted);
}

/* 按钮样式增强 */
.btn {
    font-weight: 500;
    border-radius: var(--border-radius);
    transition: var(--transition);
    border: 1px solid transparent;
    padding: 8px 16px;
    font-size: 0.875rem;
    line-height: 1.5;
}

.btn-primary {
    background: var(--primary-color);
    color: #fff;
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: var(--primary-hover);
    border-color: var(--primary-hover);
    color: #fff;
}

.btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: #fff;
}

.btn-outline-secondary {
    border-color: var(--border-color);
    color: var(--text-muted);
    background: transparent;
}

.btn-outline-secondary:hover {
    background: var(--bg-tertiary);
    border-color: var(--text-muted);
    color: var(--text-color);
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.8125rem;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: 16px;
    }
}

@media (max-width: 768px) {
    /* 防止横向滚动条 */
    body {
        overflow-x: hidden;
    }

    .admin-wrapper {
        overflow-x: hidden;
    }

    .main-content {
        overflow-x: hidden;
        width: 100%;
        max-width: 100vw;
    }

    .content-wrapper {
        overflow-x: hidden;
        width: 100%;
        max-width: 100%;
        padding: 12px;
    }

    /* 移动端侧边栏优化 */
    .sidebar {
        transform: translateX(-100%);
        z-index: 1050;
        width: 280px;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        height: 100vh !important;
        background: var(--bg-primary) !important;
        box-shadow: 2px 0 10px rgba(0,0,0,0.1) !important;
        transition: transform 0.3s ease !important;
    }

    .sidebar.show {
        transform: translateX(0) !important;
    }

    /* 侧边栏遮罩层 */
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        z-index: 1049;
        display: none;
    }

    .sidebar-overlay.show {
        display: block;
    }

    .main-content {
        margin-left: 0;
    }

    /* 顶部导航固定显示 */
    .top-header {
        padding: 0 12px;
        height: 56px;
        overflow-x: hidden;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        z-index: 1040 !important;
        width: 100% !important;
        background: var(--bg-primary) !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    }

    /* 为固定头部留出空间 */
    .main-content {
        padding-top: 56px !important;
    }

    .content-wrapper {
        padding-top: 12px !important;
    }

    .page-title {
        font-size: 1.125rem;
    }

    /* 移动端概览模块优化 - 一行显示两个 */
    .stats-grid {
        display: grid !important;
        grid-template-columns: 1fr 1fr !important;
        gap: 8px !important;
        margin-bottom: 16px !important;
    }

    .stat-card {
        flex: 1 1 25% !important;
        min-width: 0 !important;
        width: auto !important;
        padding: 6px 2px !important;
        margin: 0 !important;
        text-align: center !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
    }

    .stat-card .stat-header {
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        margin-bottom: 2px !important;
    }

    .stat-card .stat-icon {
        display: none !important;
    }

    .stat-card .stat-title {
        font-size: 0.65rem !important;
        line-height: 1.1 !important;
        margin-bottom: 2px !important;
        text-align: center !important;
    }

    .stat-card .stat-value {
        font-size: 1rem !important;
        font-weight: 600 !important;
        margin-bottom: 1px !important;
        line-height: 1.1 !important;
    }

    .stat-card .stat-change {
        font-size: 0.6rem !important;
        line-height: 1 !important;
        margin-bottom: 0 !important;
    }

    .stat-card .stat-change i {
        display: none !important;
    }

    .stat-card .stat-change span {
        display: block !important;
        text-align: center !important;
    }

    /* 控制台页面图表区域 - 调换快捷操作和卡密使用趋势位置 */
    /* 针对控制台页面的特定行布局 */
    .row .col-lg-8,
    .row .col-lg-4 {
        width: 100% !important;
        max-width: 100% !important;
        flex: 0 0 100% !important;
    }

    /* 快捷操作模块在移动端显示在前面 */
    .row .col-lg-4 {
        order: 1 !important;
        margin-bottom: 16px !important;
    }

    /* 卡密使用趋势模块在移动端显示在后面 */
    .row .col-lg-8 {
        order: 2 !important;
    }

    .stat-value {
        font-size: 1.2rem !important;
    }

    .stat-title {
        font-size: 0.7rem !important;
    }

    .stat-change {
        font-size: 0.7rem !important;
    }

    .chart-container {
        padding: 12px;
        overflow-x: hidden;
        width: 100%;
        max-width: 100%;
    }

    .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    /* 表格容器支持左右滑动 */
    .table-container {
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch !important;
        width: 100% !important;
        max-width: 100% !important;
        border-radius: 8px;
        box-shadow: inset 0 0 0 1px rgba(0,0,0,0.1);
    }

    .table {
        min-width: 800px !important;
        width: auto !important;
        margin: 0 !important;
    }

    .table th,
    .table td {
        padding: 8px 6px;
        font-size: 0.75rem;
        white-space: nowrap;
        min-width: 80px;
    }

    /* 特定列的最小宽度 */
    .table th:first-child,
    .table td:first-child {
        min-width: 40px;
    }

    .table th:nth-child(2),
    .table td:nth-child(2) {
        min-width: 50px;
    }

    .table th:nth-child(3),
    .table td:nth-child(3) {
        min-width: 150px;
        max-width: 200px;
    }

    .modal-dialog {
        margin: 16px;
    }

    .modal-body {
        padding: 16px;
    }

    .category-children {
        margin-left: 20px;
    }

    /* 页面头部响应式 - 生成卡密按钮单独一行 */
    .page-header .d-flex,
    .d-flex.justify-content-between.align-items-center.mb-4 {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 12px !important;
    }

    .page-header .d-flex > div:last-child,
    .d-flex.justify-content-between.align-items-center.mb-4 > div:last-child {
        width: 100%;
    }

    .page-header .d-flex .btn,
    .d-flex.justify-content-between.align-items-center.mb-4 .btn {
        width: 100%;
        padding: 12px !important;
        font-size: 1rem !important;
    }

    /* 筛选表单响应式 - 所有内容在一行显示且占用一屏，无横向滚动 */
    .card .card-body .row.g-3 {
        margin: 0 !important;
        display: flex !important;
        flex-wrap: nowrap !important;
        gap: 4px !important;
        align-items: flex-end !important;
        width: 100% !important;
        overflow: visible !important;
    }

    /* 卡密管理页面筛选布局 - 3列布局 */
    .card .card-body .col-md-3:nth-child(1) {
        flex: 0 0 20% !important;
        max-width: 20% !important;
        padding: 0 !important;
        margin-bottom: 0 !important;
    }

    .card .card-body .col-md-6:nth-child(2) {
        flex: 1 1 50% !important;
        max-width: 50% !important;
        padding: 0 !important;
        margin-bottom: 0 !important;
    }

    .card .card-body .col-md-3:nth-child(3) {
        flex: 0 0 30% !important;
        max-width: 30% !important;
        padding: 0 !important;
        margin-bottom: 0 !important;
    }

    /* 内容管理页面筛选布局 - 4列布局 */
    .card .card-body .col-md-3:nth-child(2) {
        flex: 0 0 25% !important;
        max-width: 25% !important;
        padding: 0 !important;
        margin-bottom: 0 !important;
    }

    .card .card-body .col-md-2:nth-child(3) {
        flex: 0 0 15% !important;
        max-width: 15% !important;
        padding: 0 !important;
        margin-bottom: 0 !important;
    }

    .card .card-body .col-md-4:nth-child(4) {
        flex: 1 1 35% !important;
        max-width: 35% !important;
        padding: 0 !important;
        margin-bottom: 0 !important;
    }

    .card .card-body .col-md-3:nth-child(5) {
        flex: 0 0 25% !important;
        max-width: 25% !important;
        padding: 0 !important;
        margin-bottom: 0 !important;
    }

    /* 筛选项内部布局 - 文字和控件上下居中对齐 */
    .card .card-body .row.align-items-center.g-1 {
        display: flex !important;
        flex-direction: row !important;
        align-items: center !important;
        justify-content: center !important;
        margin: 0 !important;
        gap: 4px !important;
        width: 100% !important;
        height: 100% !important;
        min-height: 32px !important;
    }

    .card .card-body .col-auto {
        flex: 0 0 auto !important;
        padding: 0 !important;
        margin-bottom: 0 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    .card .card-body .col {
        flex: 1 1 auto !important;
        padding: 0 !important;
        display: flex !important;
        align-items: center !important;
    }

    /* 筛选表单控件优化 - 与标签居中对齐 */
    .card .card-body .form-label.small {
        font-size: 0.65rem !important;
        margin-bottom: 0 !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        line-height: 1.2 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        height: 28px !important;
    }

    .card .card-body .form-select-sm,
    .card .card-body .form-control-sm {
        font-size: 0.7rem !important;
        padding: 3px 4px !important;
        width: 100% !important;
        min-width: 0 !important;
        height: 28px !important;
        display: flex !important;
        align-items: center !important;
    }

    /* 按钮组优化 - 调整按钮顺序：重置在前，搜索在后 */
    .card .card-body .d-flex.gap-2 {
        flex-direction: column-reverse !important;
        gap: 2px !important;
        width: 100% !important;
        height: 100% !important;
        justify-content: center !important;
        align-items: center !important;
    }

    .card .card-body .btn-sm {
        font-size: 0.65rem !important;
        padding: 3px 6px !important;
        white-space: nowrap !important;
        width: 100% !important;
        height: 13px !important;
        line-height: 1 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }
}

@media (max-width: 480px) {
    /* 更严格的防止横向滚动 */
    * {
        max-width: 100% !important;
        box-sizing: border-box !important;
    }

    .content-wrapper {
        padding: 8px !important;
        overflow-x: hidden !important;
    }



    .stat-card .stat-header {
        margin-bottom: 1px !important;
    }

    .stat-card .stat-title {
        font-size: 0.6rem !important;
        margin-bottom: 1px !important;
        line-height: 1 !important;
    }

    .stat-card .stat-value {
        font-size: 0.9rem !important;
        margin-bottom: 1px !important;
        line-height: 1 !important;
    }

    .stat-card .stat-change {
        font-size: 0.55rem !important;
        line-height: 1 !important;
    }

    /* 控制台页面图表区域进一步优化 */
    .row .col-lg-4 {
        margin-bottom: 12px !important;
    }

    /* 快捷操作按钮优化 - 确保4个按钮都显示 */
    .col-lg-4 .chart-container .row.g-3 {
        gap: 6px !important;
    }

    .col-lg-4 .chart-container .col-6 {
        flex: 0 0 calc(50% - 3px) !important;
        max-width: calc(50% - 3px) !important;
    }

    .col-lg-4 .chart-container .btn {
        padding: 10px 6px !important;
        min-height: 70px !important;
        font-size: 0.8rem !important;
    }

    .col-lg-4 .chart-container .btn .fs-4 {
        font-size: 1rem !important;
        margin-bottom: 3px !important;
    }

    /* 480px以下 - 卡密设置和前端设置页面进一步优化 */
    .tab-pane#popup .row.g-3 {
        gap: 6px !important;
    }

    .tab-pane#popup .row.align-items-center.g-2,
    .tab-pane#popup .row.align-items-start.g-2 {
        gap: 3px !important;
        margin-bottom: 6px !important;
    }

    .tab-pane#popup .col-auto {
        min-width: 70px !important;
        padding-right: 6px !important;
    }

    .tab-pane#popup .form-label {
        font-size: 0.8rem !important;
    }

    .tab-pane#popup .text-muted {
        font-size: 0.7rem !important;
    }

    .tab-pane#popup .alert {
        padding: 6px 10px !important;
        margin-top: 6px !important;
    }

    .tab-pane#popup .alert .alert-heading {
        font-size: 0.8rem !important;
        margin-bottom: 3px !important;
    }

    .tab-pane#popup .alert li {
        font-size: 0.7rem !important;
        margin-bottom: 1px !important;
    }

    /* 前端设置页面进一步优化 */
    .tab-pane .row.align-items-center.g-2,
    .tab-pane .row.align-items-start.g-2 {
        gap: 3px !important;
        margin-bottom: 4px !important;
    }

    .tab-pane .col-auto {
        min-width: 70px !important;
        padding-right: 4px !important;
    }

    .tab-pane .form-label {
        font-size: 0.75rem !important;
    }

    .tab-pane .text-muted {
        font-size: 0.65rem !important;
    }

    .tab-pane textarea.form-control,
    .tab-pane input.form-control {
        font-size: 0.75rem !important;
        padding: 4px 6px !important;
    }

    .stat-value {
        font-size: 1rem !important;
    }

    .stat-title {
        font-size: 0.65rem !important;
    }

    .stat-change {
        font-size: 0.65rem !important;
        margin-top: 4px !important;
    }

    .stat-icon {
        width: 32px !important;
        height: 32px !important;
        font-size: 0.9rem !important;
    }

    .chart-container {
        padding: 8px !important;
        margin-bottom: 12px !important;
    }

    .chart-header {
        padding-bottom: 8px !important;
        margin-bottom: 8px !important;
    }

    .chart-title {
        font-size: 1rem !important;
    }

    /* 表格进一步优化 */
    .table {
        min-width: 800px !important;
        font-size: 0.7rem !important;
    }

    /* 最近活动表格列宽优化 */
    .table th:nth-child(1), .table td:nth-child(1) { /* 卡密 */
        width: 15% !important;
        min-width: 120px !important;
    }
    .table th:nth-child(2), .table td:nth-child(2) { /* 关联内容 */
        width: 20% !important;
        min-width: 140px !important;
    }
    .table th:nth-child(3), .table td:nth-child(3) { /* 操作类型 */
        width: 12% !important;
        min-width: 80px !important;
    }
    .table th:nth-child(4), .table td:nth-child(4) { /* 操作时间 */
        width: 18% !important;
        min-width: 120px !important;
    }
    .table th:nth-child(5), .table td:nth-child(5) { /* 使用状态 */
        width: 12% !important;
        min-width: 80px !important;
    }
    .table th:nth-child(6), .table td:nth-child(6) { /* 启用状态 */
        width: 12% !important;
        min-width: 80px !important;
    }
    .table th:nth-child(7), .table td:nth-child(7) { /* 操作 */
        width: 11% !important;
        min-width: 70px !important;
    }

    .table th,
    .table td {
        padding: 4px 3px !important;
        font-size: 0.7rem !important;
        min-width: 60px !important;
    }

    .table th:first-child,
    .table td:first-child {
        min-width: 30px !important;
    }

    .table th:nth-child(2),
    .table td:nth-child(2) {
        min-width: 40px !important;
    }

    .table th:nth-child(3),
    .table td:nth-child(3) {
        min-width: 130px !important;
        max-width: 180px !important;
    }

    .btn {
        padding: 4px 8px !important;
        font-size: 0.7rem !important;
    }

    .btn-sm {
        padding: 3px 6px !important;
        font-size: 0.65rem !important;
    }

    /* 操作按钮优化 */
    .category-action-btn {
        width: 24px !important;
        height: 24px !important;
        font-size: 0.7rem !important;
    }

    .category-actions {
        gap: 1px !important;
    }

    /* 表单控件优化 */
    .form-control,
    .form-select {
        font-size: 0.75rem !important;
        padding: 4px 6px !important;
    }

    .form-control-sm,
    .form-select-sm {
        font-size: 0.7rem !important;
        padding: 3px 5px !important;
    }

    /* 模态框优化 */
    .modal-dialog {
        margin: 8px !important;
        max-width: calc(100vw - 16px) !important;
    }

    .modal-body {
        padding: 12px !important;
    }

    .modal-header {
        padding: 12px !important;
    }

    .modal-footer {
        padding: 8px 12px !important;
    }

    /* 页面标题优化 */
    .page-header h1 {
        font-size: 1.2rem !important;
    }

    .page-header p {
        font-size: 0.8rem !important;
    }

    /* 480px以下的进一步优化 */
    /* 顶部导航进一步优化 */
    .top-header {
        height: 48px !important;
        padding: 0 8px !important;
    }

    .main-content {
        padding-top: 48px !important;
    }

    .page-title {
        font-size: 0.9rem !important;
        max-width: 150px !important;
    }

    .sidebar-toggle {
        font-size: 1rem !important;
        padding: 6px !important;
    }

    .user-avatar-simple {
        width: 24px !important;
        height: 24px !important;
    }

    .user-action-link {
        width: 24px !important;
        height: 24px !important;
        font-size: 0.8rem !important;
    }

    /* 生成卡密按钮进一步优化 */
    .d-flex.justify-content-between.align-items-center.mb-4 .btn {
        padding: 10px !important;
        font-size: 0.9rem !important;
    }

    /* 筛选表单进一步优化 - 480px以下屏幕 */
    .card .card-body .row.g-3 {
        gap: 2px !important;
    }

    /* 卡密管理页面筛选布局 - 小屏幕优化 */
    .card .card-body .col-md-3:nth-child(1) {
        flex: 0 0 18% !important;
        max-width: 18% !important;
    }

    .card .card-body .col-md-6:nth-child(2) {
        flex: 1 1 52% !important;
        max-width: 52% !important;
    }

    .card .card-body .col-md-3:nth-child(3) {
        flex: 0 0 30% !important;
        max-width: 30% !important;
    }

    /* 内容管理页面筛选布局 - 小屏幕优化 */
    .card .card-body .col-md-3:nth-child(2) {
        flex: 0 0 22% !important;
        max-width: 22% !important;
    }

    .card .card-body .col-md-2:nth-child(3) {
        flex: 0 0 13% !important;
        max-width: 13% !important;
    }

    .card .card-body .col-md-4:nth-child(4) {
        flex: 1 1 40% !important;
        max-width: 40% !important;
    }

    .card .card-body .col-md-3:nth-child(5) {
        flex: 0 0 25% !important;
        max-width: 25% !important;
    }

    .card .card-body .form-label.small {
        font-size: 0.6rem !important;
        line-height: 1.2 !important;
        height: 26px !important;
    }

    .card .card-body .form-select-sm,
    .card .card-body .form-control-sm {
        font-size: 0.65rem !important;
        padding: 2px 3px !important;
        height: 26px !important;
    }

    .card .card-body .btn-sm {
        font-size: 0.6rem !important;
        padding: 2px 4px !important;
        height: 12px !important;
        line-height: 1 !important;
    }

    /* 系统设置页面优化 */
    .nav-tabs {
        flex-wrap: nowrap !important;
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch !important;
        border-bottom: 1px solid #dee2e6 !important;
    }

    .nav-tabs .nav-link {
        white-space: nowrap !important;
        font-size: 0.8rem !important;
        padding: 8px 12px !important;
        min-width: auto !important;
    }

    .nav-tabs .nav-link i {
        font-size: 0.7rem !important;
    }

    /* 设置表单优化 */
    .tab-content .p-4 {
        padding: 12px !important;
    }

    .tab-content .row.g-1 {
        margin: 0 !important;
        flex-direction: column !important;
    }

    .tab-content .col-auto {
        width: 100% !important;
        padding: 0 !important;
        margin-bottom: 4px !important;
        min-width: auto !important;
    }

    .tab-content .col-5,
    .tab-content .col {
        width: 100% !important;
        padding: 0 !important;
    }

    .tab-content .form-label {
        font-size: 0.8rem !important;
        margin-bottom: 4px !important;
    }

    .tab-content .form-control,
    .tab-content .form-select {
        font-size: 0.8rem !important;
        padding: 6px 8px !important;
    }

    .tab-content .text-muted {
        font-size: 0.7rem !important;
        margin-top: 2px !important;
    }
}

/* 工具类 */
.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.shadow-sm {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.shadow {
    box-shadow: var(--box-shadow);
}

.rounded {
    border-radius: var(--border-radius);
}

.border-0 {
    border: none !important;
}

/* 分类管理样式 */
.category-table {
    margin-bottom: 0;
}

.category-table thead th {
    background: var(--bg-tertiary);
    border-bottom: 2px solid var(--border-color);
    font-weight: 600;
    color: var(--text-color);
    padding: 16px;
}

.category-table tbody tr {
    border: none;
    transition: var(--transition);
}

.category-table tbody tr:hover {
    background: var(--bg-hover);
}

.category-table td {
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-light);
    vertical-align: middle;
}

.category-name-cell {
    width: 45%;
}

.category-status-cell {
    width: 15%;
}

.category-sort-cell {
    width: 15%;
}

.category-actions-cell {
    width: 25%;
}

.category-toggle {
    background: none;
    border: none;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--text-light);
    transition: var(--transition);
    font-size: 0.875rem;
    border-radius: 4px;
}

.category-toggle:hover {
    color: var(--primary-color);
    background: var(--bg-tertiary);
}

.category-toggle-placeholder {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.category-icon {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
    border-radius: 6px;
    background: var(--bg-tertiary);
}

.category-info {
    flex: 1;
}

.category-name {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 4px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    display: inline-block;
}

.category-name:hover {
    color: var(--primary-color);
}

/* 层级颜色区分 */
.category-level-1 {
    color: var(--primary-color) !important;
    font-weight: 700;
}

.category-level-2 {
    color: var(--success-color) !important;
    font-weight: 600;
}

.category-level-3 {
    color: var(--warning-color) !important;
    font-weight: 500;
}

.level-1 .category-name {
    color: var(--primary-color);
    font-weight: 700;
}

.level-2 .category-name {
    color: var(--success-color);
    font-weight: 600;
}

.level-3 .category-name {
    color: var(--warning-color);
    font-weight: 500;
}

/* 选中状态的红色圆点 */
.category-name.selected::after {
    content: '';
    position: absolute;
    top: 50%;
    right: -12px;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    background-color: var(--danger-color);
    border-radius: 50%;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        opacity: 1;
        transform: translateY(-50%) scale(1);
    }
    50% {
        opacity: 0.7;
        transform: translateY(-50%) scale(1.2);
    }
    100% {
        opacity: 1;
        transform: translateY(-50%) scale(1);
    }
}

.category-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.8125rem;
}

.category-indent {
    width: 24px;
    height: 1px;
}

.sort-value {
    font-weight: 500;
    color: var(--text-color);
    background: var(--bg-tertiary);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8125rem;
}

/* 层级样式 */
.level-1 {
    background: var(--bg-primary);
}

.level-2 {
    background: var(--bg-secondary);
}

.level-3 {
    background: var(--bg-tertiary);
}

/* 移除层级边框线 */
.level-2 td,
.level-3 td {
    border-left: none;
}

.level-3 td {
    border-left: none;
}

/* 展开/收起状态 */
.category-children {
    display: table-row;
}

.category-children.collapsed {
    display: none;
}

.text-purple {
    color: #8b5cf6 !important;
}

/* 分类状态切换样式 */
.category-status-switch {
    cursor: pointer;
}

.category-status-switch:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 禁用状态的分类样式 */
.category-disabled {
    opacity: 0.6;
    background-color: var(--bg-tertiary) !important;
}

.category-disabled .category-name {
    color: var(--text-light) !important;
    text-decoration: line-through;
}

.category-disabled .category-icon {
    opacity: 0.5;
}

/* 状态切换动画 */
.form-switch .form-check-input {
    transition: var(--transition);
}

.form-switch .form-check-input:focus {
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* 居中对齐优化 */
.category-table th,
.category-table td {
    vertical-align: middle;
}

.category-name-cell .d-flex {
    min-height: 40px;
}

.category-info {
    text-align: left;
}

.category-name {
    font-size: 0.875rem;
    line-height: 1.4;
    margin-bottom: 0;
}

/* 层级缩进优化 */
.category-indent {
    width: 20px;
    height: 1px;
    flex-shrink: 0;
}

.level-2 .category-indent {
    width: 24px;
}

.level-3 .category-indent {
    width: 20px;
}

/* 图标颜色优化 */
.category-icon .text-primary {
    color: var(--primary-color) !important;
}

.category-icon .text-warning {
    color: #f59e0b !important;
}

.category-icon .text-success {
    color: var(--success-color) !important;
}

.category-icon .text-info {
    color: var(--info-color) !important;
}

.category-icon .text-secondary {
    color: var(--text-muted) !important;
}

.category-icon .text-purple {
    color: #8b5cf6 !important;
}



/* 分类操作按钮优化 */
.category-actions {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.category-action-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.875rem;
    background: transparent;
    color: var(--text-muted);
}

.category-action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.category-action-btn:active {
    transform: translateY(0);
}

/* 添加按钮 */
.category-action-btn.add-btn {
    background: var(--success-light);
    color: var(--success-color);
}

.category-action-btn.add-btn:hover {
    background: var(--success-color);
    color: #fff;
}

/* 编辑按钮 */
.category-action-btn.edit-btn {
    background: var(--primary-light);
    color: var(--primary-color);
}

.category-action-btn.edit-btn:hover {
    background: var(--primary-color);
    color: #fff;
}

/* 删除按钮 */
.category-action-btn.delete-btn {
    background: var(--danger-light);
    color: var(--danger-color);
}

.category-action-btn.delete-btn:hover {
    background: var(--danger-color);
    color: #fff;
}



/* 按钮禁用状态 */
.category-action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.category-action-btn:disabled:hover {
    transform: none !important;
    box-shadow: none !important;
}

/* 按钮焦点状态 */
.category-action-btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* 禁用分类的按钮样式 */
.category-disabled .category-action-btn {
    opacity: 0.6;
}

.category-disabled .category-action-btn:hover {
    transform: none;
    box-shadow: none;
}

/* 批量选择样式 */
.category-checkbox {
    cursor: pointer;
    width: 18px;
    height: 18px;
    border: 2px solid rgba(0, 0, 0, 0.8);
    border-radius: 4px;
    background-color: #ffffff;
    transition: var(--transition);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.category-checkbox:hover {
    border-color: rgba(0, 0, 0, 0.9);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15), 0 0 0 3px rgba(0, 0, 0, 0.1);
}

.category-checkbox:checked {
    background-color: rgba(0, 0, 0, 0.8);
    border-color: rgba(0, 0, 0, 0.8);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='white'%3e%3cpath fill-rule='evenodd' d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z' clip-rule='evenodd'/%3e%3c/svg%3e");
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
}

.category-checkbox:focus {
    outline: none;
    border-color: rgba(0, 0, 0, 0.9);
    box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.2);
}

.category-checkbox:indeterminate {
    background-color: rgba(0, 0, 0, 0.8);
    border-color: rgba(0, 0, 0, 0.8);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='white'%3e%3cpath fill-rule='evenodd' d='M4 10a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1z' clip-rule='evenodd'/%3e%3c/svg%3e");
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
}

/* 全选复选框样式 */
#selectAll {
    width: 18px;
    height: 18px;
    border: 2px solid rgba(0, 0, 0, 0.8);
    border-radius: 4px;
    background-color: #ffffff;
    transition: var(--transition);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

#selectAll:hover {
    border-color: rgba(0, 0, 0, 0.9);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15), 0 0 0 3px rgba(0, 0, 0, 0.1);
}

#selectAll:checked {
    background-color: rgba(0, 0, 0, 0.8);
    border-color: rgba(0, 0, 0, 0.8);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='white'%3e%3cpath fill-rule='evenodd' d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z' clip-rule='evenodd'/%3e%3c/svg%3e");
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
}

#selectAll:focus {
    outline: none;
    border-color: rgba(0, 0, 0, 0.9);
    box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.2);
}

#selectAll:indeterminate {
    background-color: rgba(0, 0, 0, 0.8);
    border-color: rgba(0, 0, 0, 0.8);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='white'%3e%3cpath fill-rule='evenodd' d='M4 10a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1z' clip-rule='evenodd'/%3e%3c/svg%3e");
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
}

.category-item.selected-for-delete {
    background-color: var(--danger-light) !important;
}

.category-item.selected-for-delete .category-name {
    color: var(--danger-color) !important;
}

/* 复选框容器样式 */
.form-check {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 40px;
}

.form-check-input {
    margin: 0;
}

/* 批量删除按钮动画 */
#batchDeleteBtn {
    transition: var(--transition);
}

#batchDeleteBtn:hover {
    transform: translateY(-1px);
    box-shadow: var(--box-shadow-lg);
}

/* 排序输入框样式 */
.sort-input {
    width: 70px;
    height: 32px;
    text-align: center;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 14px;
    padding: 4px 8px;
    transition: var(--transition);
    margin: 0 auto;
    display: block;
}

.sort-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
    outline: none;
}

.sort-input:hover {
    border-color: var(--primary-color);
}

/* 排序输入加载状态 */
.sort-input.loading {
    background: #f8f9fa;
    pointer-events: none;
}

.sort-input.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 8px;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    border: 2px solid #dee2e6;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
}

/* 排序单元格样式 */
.category-sort-cell {
    padding: 8px 4px;
    vertical-align: middle;
}

/* 统计卡片样式 */
.stats-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    transition: var(--transition);
    overflow: hidden;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.stats-card .card-body {
    padding: 1.5rem;
}

.stats-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.stats-icon.bg-primary {
    background: linear-gradient(135deg, var(--primary-color), #4f46e5);
}

.stats-icon.bg-success {
    background: linear-gradient(135deg, var(--success-color), #059669);
}

.stats-icon.bg-warning {
    background: linear-gradient(135deg, var(--warning-color), #d97706);
}

.stats-icon.bg-info {
    background: linear-gradient(135deg, var(--info-color), #0284c7);
}

.stats-icon.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.stats-icon.bg-gradient-success {
    background: linear-gradient(135deg, #11998e, #38ef7d);
}

.stats-icon.bg-gradient-warning {
    background: linear-gradient(135deg, #f093fb, #f5576c);
}

.stats-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-color);
    margin: 0;
    line-height: 1.2;
}

.stats-label {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin: 0;
    font-weight: 500;
}

/* 响应式统计卡片 */
@media (max-width: 768px) {
    .stats-card .card-body {
        padding: 1rem;
    }

    .stats-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .stats-number {
        font-size: 1.5rem;
    }

    .stats-label {
        font-size: 0.8rem;
    }
}

/* 分页容器 */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
    border-radius: 0 0 8px 8px;
    margin: 0 -1.5rem -1.5rem -1.5rem;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}

/* 分页信息区域 */
.pagination-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.875rem;
}

/* 每页显示条数选择器 */
.page-size-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.page-size-label,
.page-size-unit {
    color: var(--text-muted);
    font-size: 0.875rem;
    white-space: nowrap;
}

.page-size-select {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 0.25rem 1.5rem 0.25rem 0.5rem;
    font-size: 0.875rem;
    background: #fff url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") no-repeat right 0.5rem center/12px 12px;
    appearance: none;
    min-width: 60px;
    cursor: pointer;
    transition: var(--transition);
}

.page-size-select:focus {
    border-color: var(--primary-color);
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

.page-size-select:hover {
    border-color: #adb5bd;
}

/* 分页导航区域 */
.pagination-nav {
    display: flex;
    align-items: center;
}

.pagination-nav .pagination {
    margin-bottom: 0;
}

/* 通用分页样式 - 覆盖ThinkPHP默认样式 */
.pagination-container .pagination,
.pagination-nav .pagination {
    margin-bottom: 0;
}

.pagination-container .page-link,
.pagination-nav .page-link {
    color: var(--primary-color) !important;
    border: 1px solid #dee2e6 !important;
    padding: 0.375rem 0.75rem !important;
    margin-left: -1px;
    line-height: 1.25;
    text-decoration: none !important;
    background-color: #fff !important;
    border-radius: 0 !important;
    transition: var(--transition);
    font-size: 0.875rem !important;
    display: inline-block;
}

.pagination-container .page-link:hover,
.pagination-nav .page-link:hover {
    color: var(--primary-hover) !important;
    background-color: #e9ecef !important;
    border-color: #dee2e6 !important;
}

.pagination-container .page-item.active .page-link,
.pagination-nav .page-item.active .page-link {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: #fff !important;
    font-weight: 600;
}

.pagination-container .page-item.disabled .page-link,
.pagination-nav .page-item.disabled .page-link {
    color: #6c757d !important;
    background-color: #fff !important;
    border-color: #dee2e6 !important;
    cursor: not-allowed;
}

.pagination-container .page-item:first-child .page-link,
.pagination-nav .page-item:first-child .page-link {
    border-top-left-radius: 0.375rem !important;
    border-bottom-left-radius: 0.375rem !important;
}

.pagination-container .page-item:last-child .page-link,
.pagination-nav .page-item:last-child .page-link {
    border-top-right-radius: 0.375rem !important;
    border-bottom-right-radius: 0.375rem !important;
}

/* 确保分页链接的基础样式 */
.pagination-container a.page-link,
.pagination-nav a.page-link {
    color: var(--primary-color) !important;
    background-color: #fff !important;
}

.pagination-container a.page-link:hover,
.pagination-nav a.page-link:hover {
    color: var(--primary-hover) !important;
    background-color: #e9ecef !important;
}

/* 分页数字样式 */
.pagination-container .page-link,
.pagination-nav .page-link {
    min-width: 2.5rem;
    text-align: center;
    font-weight: 500;
}

/* 强制覆盖所有分页样式 */
.pagination-container ul.pagination li.page-item a.page-link,
.pagination-container ul.pagination li.page-item span.page-link,
.pagination-nav ul.pagination li.page-item a.page-link,
.pagination-nav ul.pagination li.page-item span.page-link {
    color: var(--primary-color) !important;
    border: 1px solid #dee2e6 !important;
    padding: 0.375rem 0.75rem !important;
    background-color: #fff !important;
    text-decoration: none !important;
    font-size: 0.875rem !important;
    min-width: 2.5rem !important;
    text-align: center !important;
    display: inline-block !important;
    border-radius: 0 !important;
}

.pagination-container ul.pagination li.page-item a.page-link:hover,
.pagination-nav ul.pagination li.page-item a.page-link:hover {
    color: var(--primary-hover) !important;
    background-color: #e9ecef !important;
    border-color: #dee2e6 !important;
}

.pagination-container ul.pagination li.page-item.active a.page-link,
.pagination-container ul.pagination li.page-item.active span.page-link,
.pagination-nav ul.pagination li.page-item.active a.page-link,
.pagination-nav ul.pagination li.page-item.active span.page-link {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: #fff !important;
    font-weight: 600 !important;
}

.pagination-container ul.pagination li.page-item.disabled a.page-link,
.pagination-container ul.pagination li.page-item.disabled span.page-link,
.pagination-nav ul.pagination li.page-item.disabled a.page-link,
.pagination-nav ul.pagination li.page-item.disabled span.page-link {
    color: #6c757d !important;
    background-color: #fff !important;
    border-color: #dee2e6 !important;
    cursor: not-allowed !important;
}

.pagination-container ul.pagination li.page-item:first-child a.page-link,
.pagination-container ul.pagination li.page-item:first-child span.page-link,
.pagination-nav ul.pagination li.page-item:first-child a.page-link,
.pagination-nav ul.pagination li.page-item:first-child span.page-link {
    border-top-left-radius: 0.375rem !important;
    border-bottom-left-radius: 0.375rem !important;
}

.pagination-container ul.pagination li.page-item:last-child a.page-link,
.pagination-container ul.pagination li.page-item:last-child span.page-link,
.pagination-nav ul.pagination li.page-item:last-child a.page-link,
.pagination-nav ul.pagination li.page-item:last-child span.page-link {
    border-top-right-radius: 0.375rem !important;
    border-bottom-right-radius: 0.375rem !important;
}

/* 全局分页样式覆盖 - 确保所有分页都有样式 */
.pagination {
    margin-bottom: 0 !important;
}

.pagination .page-link {
    color: var(--primary-color) !important;
    border: 1px solid #dee2e6 !important;
    padding: 0.375rem 0.75rem !important;
    background-color: #fff !important;
    text-decoration: none !important;
    font-size: 0.875rem !important;
    min-width: 2.5rem !important;
    text-align: center !important;
    display: inline-block !important;
    border-radius: 0 !important;
    transition: all 0.2s ease !important;
}

.pagination .page-link:hover {
    color: var(--primary-hover) !important;
    background-color: #e9ecef !important;
    border-color: #dee2e6 !important;
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: #fff !important;
    font-weight: 600 !important;
}

.pagination .page-item.disabled .page-link {
    color: #6c757d !important;
    background-color: #fff !important;
    border-color: #dee2e6 !important;
    cursor: not-allowed !important;
}

.pagination .page-item:first-child .page-link {
    border-top-left-radius: 0.375rem !important;
    border-bottom-left-radius: 0.375rem !important;
}

.pagination .page-item:last-child .page-link {
    border-top-right-radius: 0.375rem !important;
    border-bottom-right-radius: 0.375rem !important;
}

/* 响应式分页 */
@media (max-width: 768px) {
    .pagination-container {
        flex-direction: column;
        gap: 0.5rem;
        align-items: stretch;
        padding: 8px 12px !important;
        overflow-x: hidden !important;
    }

    .pagination-info {
        flex-direction: row;
        gap: 0.5rem;
        text-align: center;
        font-size: 0.75rem !important;
        justify-content: space-between;
        flex-wrap: wrap;
    }

    .pagination-nav {
        justify-content: center;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .pagination-nav .pagination {
        flex-wrap: nowrap !important;
        min-width: max-content;
    }

    .page-size-selector {
        justify-content: center;
        font-size: 0.75rem !important;
    }

    .page-size-select {
        font-size: 0.75rem !important;
        padding: 0.2rem 1rem 0.2rem 0.4rem !important;
        min-width: 50px !important;
    }

    .page-size-label,
    .page-size-unit {
        font-size: 0.75rem !important;
    }
}

@media (max-width: 576px) {
    .pagination-container {
        margin: 0 -8px -8px -8px !important;
        padding: 6px 8px !important;
    }

    .pagination-nav .page-link {
        padding: 0.2rem 0.4rem !important;
        font-size: 0.7rem !important;
        min-width: 2rem !important;
    }

    .pagination-info {
        font-size: 0.7rem !important;
        gap: 0.3rem;
    }

    .page-size-selector {
        font-size: 0.7rem !important;
    }

    .page-size-select {
        font-size: 0.7rem !important;
        padding: 0.15rem 0.8rem 0.15rem 0.3rem !important;
        min-width: 45px !important;
    }
}



/* 分类表格响应式优化 */
@media (max-width: 768px) {
    /* 分类表格容器 */
    .category-table {
        min-width: 700px !important;
    }

    .category-table thead th {
        padding: 8px 4px;
        font-size: 0.75rem;
        white-space: nowrap;
    }

    .category-table td {
        padding: 6px 4px;
        font-size: 0.75rem;
    }

    .category-name-cell {
        min-width: 200px;
    }

    .category-status-cell {
        min-width: 60px;
    }

    .category-sort-cell {
        min-width: 80px;
    }

    .category-actions-cell {
        min-width: 120px;
    }

    .category-name {
        font-size: 0.75rem;
    }

    .category-meta {
        font-size: 0.7rem;
    }

    .category-icon {
        width: 20px;
        height: 20px;
        font-size: 0.8rem;
    }

    .category-toggle {
        width: 18px;
        height: 18px;
        font-size: 0.7rem;
    }

    .category-indent {
        width: 12px;
    }

    .btn-group-sm .btn {
        padding: 3px 5px;
        font-size: 0.7rem;
    }

    .category-action-btn {
        width: 24px;
        height: 24px;
        font-size: 0.75rem;
    }

    .category-actions {
        gap: 1px;
    }

    /* 排序输入框 */
    .sort-input {
        width: 50px !important;
        height: 28px !important;
        font-size: 0.7rem !important;
        padding: 2px 4px !important;
    }
}

@media (max-width: 480px) {
    /* 更小屏幕的分类表格优化 */
    .category-table {
        min-width: 600px !important;
    }

    .category-table thead th {
        padding: 6px 2px;
        font-size: 0.7rem;
    }

    .category-table td {
        padding: 4px 2px;
        font-size: 0.7rem;
    }

    .category-name-cell {
        min-width: 160px;
    }

    .category-status-cell {
        min-width: 50px;
    }

    .category-sort-cell {
        min-width: 60px;
    }

    .category-actions-cell {
        min-width: 100px;
    }

    .category-name {
        font-size: 0.7rem;
    }

    .category-icon {
        width: 16px;
        height: 16px;
        font-size: 0.7rem;
    }

    .category-toggle {
        width: 16px;
        height: 16px;
        font-size: 0.65rem;
    }

    .category-indent {
        width: 10px;
    }

    .category-action-btn {
        width: 20px;
        height: 20px;
        font-size: 0.7rem;
    }

    .sort-input {
        width: 40px !important;
        height: 24px !important;
        font-size: 0.65rem !important;
        padding: 1px 3px !important;
    }
}

/* 内容管理样式 */
.content-thumb {
    width: 56px;
    height: 36px;
    object-fit: cover;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

/* 表单样式优化 */
.form-control {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 8px 12px;
    font-size: 0.875rem;
    transition: var(--transition);
    background-color: var(--bg-primary);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    outline: none;
}

.form-select {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 8px 12px;
    font-size: 0.875rem;
    transition: var(--transition);
    background-color: var(--bg-primary);
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    outline: none;
}

.form-label {
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 6px;
    font-size: 0.875rem;
}

.form-text {
    color: var(--text-light);
    font-size: 0.8125rem;
    margin-top: 4px;
}

.form-check-input {
    border: 1px solid var(--border-color);
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-label {
    color: var(--text-color);
    font-size: 0.875rem;
}

/* 模态框样式优化 */
.modal-content {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-lg);
}

.modal-header {
    border-bottom: 1px solid var(--border-light);
    padding: 20px 24px;
}

.modal-title {
    font-weight: 600;
    color: var(--text-color);
    font-size: 1.125rem;
}

.modal-body {
    padding: 24px;
}

.modal-footer {
    border-top: 1px solid var(--border-light);
    padding: 16px 24px;
}

/* 分页样式优化 */
.pagination {
    margin: 0;
}

.page-link {
    color: var(--text-muted);
    border: 1px solid var(--border-color);
    padding: 6px 12px;
    font-size: 0.875rem;
    transition: var(--transition);
}

.page-link:hover {
    color: var(--primary-color);
    background-color: var(--bg-tertiary);
    border-color: var(--primary-color);
}

.page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: #fff;
}

.page-item.disabled .page-link {
    color: var(--text-light);
    background-color: var(--bg-secondary);
    border-color: var(--border-light);
}

/* 标签页样式优化 */
.nav-tabs {
    border-bottom: 1px solid var(--border-color);
}

.nav-tabs .nav-link {
    border: none;
    border-bottom: 2px solid transparent;
    color: var(--text-muted);
    padding: 12px 16px;
    font-weight: 500;
    font-size: 0.875rem;
    transition: var(--transition);
}

.nav-tabs .nav-link:hover {
    color: var(--text-color);
    border-bottom-color: var(--border-color);
}

.nav-tabs .nav-link.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background: none;
}

/* 下拉菜单优化 */
.dropdown-menu {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
    padding: 6px 0;
}

.dropdown-item {
    color: var(--text-muted);
    padding: 8px 16px;
    font-size: 0.875rem;
    transition: var(--transition);
}

.dropdown-item:hover {
    color: var(--text-color);
    background-color: var(--bg-tertiary);
}

.dropdown-divider {
    border-color: var(--border-light);
}

/* 额外的视觉优化 */
.fw-bold {
    font-weight: 600 !important;
}

.text-muted {
    color: var(--text-muted) !important;
}

.small {
    font-size: 0.8125rem !important;
}

.d-flex .me-3 {
    margin-right: 12px !important;
}

.d-flex .me-2 {
    margin-right: 8px !important;
}

.d-flex .me-1 {
    margin-right: 4px !important;
}

.mt-1 {
    margin-top: 4px !important;
}

.mb-1 {
    margin-bottom: 4px !important;
}

.mb-4 {
    margin-bottom: 24px !important;
}

.p-3 {
    padding: 12px !important;
}

.bg-light {
    background-color: var(--bg-tertiary) !important;
}

.fs-4 {
    font-size: 1.125rem !important;
}

/* 按钮组优化 */
.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: var(--border-radius);
    border-bottom-left-radius: var(--border-radius);
}

.btn-group .btn:last-child {
    border-top-right-radius: var(--border-radius);
    border-bottom-right-radius: var(--border-radius);
}

/* 输入组优化 */
.input-group .form-control {
    border-radius: 0;
}

.input-group .form-control:first-child {
    border-top-left-radius: var(--border-radius);
    border-bottom-left-radius: var(--border-radius);
}

.input-group .form-control:last-child {
    border-top-right-radius: var(--border-radius);
    border-bottom-right-radius: var(--border-radius);
}

/* 代码块样式 */
code {
    background-color: var(--bg-tertiary);
    color: var(--text-color);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.8125rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* 加载状态 */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-light);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 16px;
    color: var(--border-color);
}

/* 工具提示优化 */
.tooltip {
    font-size: 0.8125rem;
}

.tooltip-inner {
    background-color: var(--text-color);
    border-radius: var(--border-radius);
}

/* 进度条 */
.progress {
    height: 6px;
    background-color: var(--bg-tertiary);
    border-radius: 3px;
    overflow: hidden;
}

.progress-bar {
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

/* 徽章 */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 12px;
}

.badge-primary {
    background-color: var(--primary-color);
    color: #fff;
}

.badge-secondary {
    background-color: var(--bg-tertiary);
    color: var(--text-muted);
}

/* 自定义分页样式 - 按照图片样式设计 */
.custom-pagination {
    display: flex !important;
    padding-left: 0 !important;
    list-style: none !important;
    margin-bottom: 0 !important;
    gap: 0.25rem !important;
}

.custom-page-item {
    display: list-item !important;
}

.custom-page-link {
    position: relative !important;
    display: block !important;
    padding: 0.5rem 0.75rem !important;
    margin: 0 !important;
    font-size: 0.875rem !important;
    text-decoration: none !important;
    color: #6c757d !important;
    background-color: #fff !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 0.375rem !important;
    transition: all 0.15s ease-in-out !important;
    min-width: 2.5rem !important;
    text-align: center !important;
    line-height: 1.25 !important;
}

.custom-page-link:hover {
    color: #495057 !important;
    background-color: #f8f9fa !important;
    border-color: #dee2e6 !important;
    text-decoration: none !important;
}

/* 活跃状态 - 蓝色背景 */
.custom-page-link.active {
    color: #fff !important;
    background-color: #007bff !important;
    border-color: #007bff !important;
    font-weight: 500 !important;
}

.custom-page-item.active .custom-page-link {
    color: #fff !important;
    background-color: #007bff !important;
    border-color: #007bff !important;
    font-weight: 500 !important;
}

/* 禁用状态 */
.custom-page-link.disabled {
    color: #6c757d !important;
    pointer-events: none !important;
    background-color: #fff !important;
    border-color: #dee2e6 !important;
    opacity: 0.65 !important;
}

.custom-page-item.disabled .custom-page-link {
    color: #6c757d !important;
    pointer-events: none !important;
    background-color: #fff !important;
    border-color: #dee2e6 !important;
    opacity: 0.65 !important;
}

/* 复选框样式 - 80%黑色边框 */
.form-check-input {
    border: 1px solid rgba(0, 0, 0, 0.8) !important;
    border-radius: 0.25rem !important;
}

.form-check-input:focus {
    border-color: rgba(0, 0, 0, 0.8) !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1) !important;
}

.form-check-input:checked {
    background-color: var(--primary-color) !important;
    border-color: rgba(0, 0, 0, 0.8) !important;
}

.form-check-input:checked:focus {
    border-color: rgba(0, 0, 0, 0.8) !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1) !important;
}

/* 移动端专用样式 - 防止横向滚动和优化显示 */
@media (max-width: 768px) {
    /* 全局防止横向滚动 */
    html, body {
        overflow-x: hidden !important;
        max-width: 100vw !important;
    }

    /* 确保所有容器不超出屏幕宽度 */
    .container, .container-fluid, .row, .col, [class*="col-"] {
        max-width: 100% !important;
        overflow-x: hidden !important;
    }

    /* 移动端概览模块优化 - 一行显示两个 */
    .stats-grid {
        display: grid !important;
        grid-template-columns: 1fr 1fr !important;
        gap: 8px !important;
        margin-bottom: 16px !important;
    }

    .stat-card {
        padding: 12px !important;
        min-height: auto !important;
    }

    .stat-card .stat-header {
        margin-bottom: 8px !important;
    }

    .stat-card .stat-title {
        font-size: 0.75rem !important;
        margin-bottom: 4px !important;
    }

    .stat-card .stat-value {
        font-size: 1.2rem !important;
        font-weight: 600 !important;
        margin-bottom: 4px !important;
    }

    .stat-card .stat-change {
        font-size: 0.65rem !important;
    }

    .stat-card .stat-icon {
        width: 24px !important;
        height: 24px !important;
        font-size: 0.8rem !important;
    }

    /* 控制台页面模块居中显示 */
    .chart-container {
        text-align: center !important;
    }

    .chart-container .chart-header {
        justify-content: center !important;
        text-align: center !important;
    }

    .chart-container .row {
        justify-content: center !important;
    }

    /* 快捷操作模块居中 */
    .chart-container .row.g-3.p-3 {
        justify-content: center !important;
        align-items: center !important;
    }

    /* 系统状态模块特定优化 - 通过标题定位 */
    .chart-container:has(.chart-title:contains("系统状态")) .row.g-3,
    .chart-container .chart-title:contains("系统状态") ~ .row.g-3,
    .chart-header:has(.chart-title:contains("系统状态")) ~ .row.g-3 {
        display: grid !important;
        grid-template-columns: 1fr 1fr !important;
        gap: 8px !important;
        justify-content: center !important;
        text-align: center !important;
    }

    /* 更直接的系统状态选择器 - 强制覆盖 */
    .col-12 .chart-container .row.g-3 {
        display: grid !important;
        grid-template-columns: 1fr 1fr !important;
        gap: 8px !important;
        justify-content: center !important;
        text-align: center !important;
        margin: 0 !important;
    }

    /* 排除快捷操作模块 */
    .col-lg-4 .chart-container .row.g-3 {
        display: flex !important;
        grid-template-columns: unset !important;
        flex-wrap: wrap !important;
    }

    /* 强制覆盖Bootstrap的col类 */
    .col-12 .chart-container .row.g-3 > .col-md-3.col-sm-6 {
        display: block !important;
        width: auto !important;
        max-width: none !important;
        flex: none !important;
        grid-column: span 1 !important;
        margin: 0 !important;
        padding: 0 4px !important;
    }

    /* 系统状态项目优化 */
    .col-12 .chart-container .row.g-3 .col-md-3 {
        grid-column: span 1 !important;
        padding: 0 4px !important;
        width: auto !important;
        max-width: none !important;
        flex: none !important;
    }

    /* 系统状态内容优化 */
    .col-12 .chart-container .row.g-3 .d-flex.align-items-center {
        padding: 12px 8px !important;
        font-size: 0.85rem !important;
    }

    .col-12 .chart-container .row.g-3 .d-flex.align-items-center .me-3 i {
        font-size: 1.2rem !important;
    }

    .col-12 .chart-container .row.g-3 .d-flex.align-items-center .fw-bold {
        font-size: 0.8rem !important;
        margin-bottom: 2px !important;
    }

    .col-12 .chart-container .row.g-3 .d-flex.align-items-center div:last-child {
        font-size: 0.75rem !important;
    }

    /* 最高优先级的系统状态模块样式 */
    body .admin-wrapper .main-content .content-wrapper .col-12 .chart-container .row.g-3 {
        display: grid !important;
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 8px !important;
        margin: 0 !important;
    }

    body .admin-wrapper .main-content .content-wrapper .col-12 .chart-container .row.g-3 > div {
        width: auto !important;
        max-width: none !important;
        flex: none !important;
        padding: 0 4px !important;
    }

    /* 控制台页面模块加宽优化 - 左右边距一样 */
    .content-wrapper {
        padding: 12px 8px !important;
    }

    /* 卡密使用趋势模块加宽 */
    .col-lg-8 .chart-container {
        margin: 0 !important;
        width: 100% !important;
    }

    /* 快捷操作模块加宽 */
    .col-lg-4 .chart-container {
        margin: 0 !important;
        width: 100% !important;
    }

    /* 系统状态模块加宽 */
    .col-12 .chart-container {
        margin: 0 !important;
        width: 100% !important;
    }

    /* 确保行容器占满宽度 */
    .row {
        margin-left: 0 !important;
        margin-right: 0 !important;
        width: 100% !important;
    }

    /* 确保列容器占满宽度 */
    .col-lg-8, .col-lg-4, .col-12 {
        padding-left: 4px !important;
        padding-right: 4px !important;
        width: 100% !important;
        max-width: 100% !important;
    }

    /* 移动端标签页优化 */
    .nav-tabs {
        flex-wrap: nowrap !important;
        overflow-x: auto !important;
        overflow-y: hidden !important;
        -webkit-overflow-scrolling: touch !important;
        scrollbar-width: none !important;
        -ms-overflow-style: none !important;
    }

    .nav-tabs::-webkit-scrollbar {
        display: none !important;
    }

    /* 卡密详情模态框移动端优化 */
    .modal-dialog {
        margin: 0.5rem !important;
        max-width: calc(100vw - 1rem) !important;
    }

    .modal-body {
        padding: 1rem 0.75rem !important;
    }

    /* 卡密详情项目一行显示 */
    .card-detail-item {
        margin-bottom: 0.75rem !important;
        padding-bottom: 0.5rem !important;
    }

    .card-detail-item .row {
        margin: 0 !important;
        align-items: center !important;
        display: flex !important;
        flex-wrap: nowrap !important;
    }

    .card-detail-item .card-detail-label {
        padding: 0 !important;
        margin-bottom: 0 !important;
        white-space: nowrap !important;
        font-size: 0.85rem !important;
        min-width: fit-content !important;
        flex-shrink: 0 !important;
        width: auto !important;
        max-width: none !important;
    }

    .card-detail-item .card-detail-value {
        padding: 0 0 0 0.5rem !important;
        margin-bottom: 0 !important;
        font-size: 0.85rem !important;
        flex: 1 !important;
        width: auto !important;
        max-width: none !important;
        overflow-wrap: break-word !important;
        word-break: break-word !important;
        line-height: 1.4 !important;
    }

    /* 内容标题特殊处理 - 允许换行 */
    .card-detail-item:has(.card-detail-label strong:contains("内容标题")) .card-detail-value,
    .card-detail-item .card-detail-value#detail-content-list {
        white-space: normal !important;
        word-break: break-word !important;
        overflow-wrap: break-word !important;
        hyphens: auto !important;
    }

    /* 卡密号码点击复制样式 - 普通显示 */
    .card-number-clickable {
        color: inherit !important;
        text-decoration: none !important;
        transition: all 0.2s ease !important;
        min-height: 44px !important; /* 触摸友好的最小高度 */
        display: inline-block !important;
        padding: 8px 4px !important;
        border-radius: 4px !important;
        background-color: transparent !important;
    }

    .card-number-clickable:hover,
    .card-number-clickable:active {
        background-color: rgba(0, 0, 0, 0.05) !important;
    }

    /* 触摸设备特殊处理 */
    @media (hover: none) and (pointer: coarse) {
        .card-number-clickable:active {
            background-color: rgba(0, 0, 0, 0.1) !important;
        }
    }

    /* 内容标题在移动端的普通显示 */
    .card-detail-value .content-title {
        color: inherit !important;
        font-size: 0.85rem !important;
        font-weight: normal !important;
        line-height: 1.4 !important;
        word-break: break-word !important;
        overflow-wrap: break-word !important;
    }

    /* 移除旧的badge样式 */
    .card-detail-value .badge {
        background: none !important;
        color: inherit !important;
        border: none !important;
        padding: 0 !important;
        font-size: 0.85rem !important;
        font-weight: normal !important;
        margin: 0 !important;
        display: inline !important;
    }

    /* 模态框标题优化 */
    .modal-title {
        font-size: 1.1rem !important;
    }

    /* 模态框关闭按钮优化 */
    .btn-close {
        padding: 0.5rem !important;
        margin: -0.5rem -0.5rem -0.5rem auto !important;
    }

    /* 确保卡密详情模态框在移动端的响应式布局 */
    .card-detail-item .col-3,
    .card-detail-item .col-9 {
        padding-left: 0 !important;
        padding-right: 0 !important;
    }

    /* 卡密号码区域特殊处理 */
    .card-detail-item .card-detail-value .d-flex {
        align-items: center !important;
        gap: 0.25rem !important;
    }

    /* 状态标签优化 */
    .card-detail-value .badge {
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        max-width: 100% !important;
    }

    /* 确保文本不会被截断 */
    .card-detail-value span {
        word-break: break-word !important;
        overflow-wrap: break-word !important;
    }

    /* 移动端模态框内容区域优化 */
    .modal-body .card {
        border: none !important;
        box-shadow: none !important;
    }

    .modal-body .card-body {
        padding: 0 !important;
    }

    .nav-tabs .nav-item {
        flex-shrink: 0 !important;
        white-space: nowrap !important;
    }

    .nav-tabs .nav-link {
        font-size: 0.8rem !important;
        padding: 8px 12px !important;
        white-space: nowrap !important;
    }

    .nav-tabs .nav-link i {
        font-size: 0.7rem !important;
        margin-right: 4px !important;
    }

    /* 标签页内容区域优化 */
    .tab-content {
        padding: 0 !important;
    }

    .tab-pane .p-4 {
        padding: 12px 8px !important;
    }

    /* 移动端表单优化 */
    .row.g-3 {
        gap: 8px !important;
    }

    .row.align-items-center.g-2 {
        gap: 4px !important;
    }

    .form-control, .form-select {
        font-size: 14px !important;
        padding: 8px 12px !important;
    }

    .btn {
        font-size: 14px !important;
        padding: 8px 16px !important;
    }

    /* 移动端列布局优化 */
    .col-auto {
        flex: 0 0 auto !important;
        width: auto !important;
        min-width: 80px !important;
    }

    .col-sm-6, .col-6 {
        flex: 1 1 auto !important;
        max-width: none !important;
    }

    /* 前端设置页面弹窗内容模块优化 */
    .col-9 .row.g-2 {
        margin: 0 !important;
    }

    /* 弹窗内容输入框加宽 */
    .col-9 .row.g-2 .col-7 {
        flex: 0 0 100% !important;
        max-width: 100% !important;
        margin-bottom: 12px !important;
    }

    /* 常用HTML标签模块调整 */
    .col-9 .row.g-2 .col-5 {
        flex: 0 0 100% !important;
        max-width: 100% !important;
    }

    /* 富文本按键一行显示三个 */
    .btn-group.btn-group-sm {
        display: flex !important;
        flex-wrap: wrap !important;
        gap: 4px !important;
    }

    .btn-group.btn-group-sm .btn {
        flex: 1 1 calc(33.333% - 3px) !important;
        max-width: calc(33.333% - 3px) !important;
        margin: 0 !important;
        font-size: 11px !important;
        padding: 4px 6px !important;
        white-space: nowrap !important;
    }

    .btn-group.btn-group-sm .btn i {
        font-size: 10px !important;
        margin-right: 2px !important;
    }

    /* 弹窗模板左右间距一样 */
    .col-9 .row.g-2 {
        padding: 0 4px !important;
    }

    .col-9 .row.g-2 .col-4 {
        flex: 1 1 calc(33.333% - 8px) !important;
        max-width: calc(33.333% - 8px) !important;
        padding: 0 4px !important;
    }

    /* 下拉菜单按钮组优化 */
    .btn-group .dropdown-toggle {
        flex: 1 1 calc(33.333% - 3px) !important;
        max-width: calc(33.333% - 3px) !important;
    }

    /* 文本域优化 */
    #notificationContent {
        width: 100% !important;
        min-height: 120px !important;
    }

    /* 常用HTML标签区域优化 */
    .bg-light.border.rounded.p-3.h-100 {
        padding: 12px !important;
        margin-top: 0 !important;
    }

    .bg-light.border.rounded.p-3.h-100 code {
        font-size: 11px !important;
        word-break: break-all !important;
    }

    /* 预览区域优化 */
    .col-5 {
        flex: 0 0 100% !important;
        max-width: 100% !important;
        margin-top: 8px !important;
    }

    /* 保存按钮区域优化 */
    .p-4.pt-0.d-flex.justify-content-end {
        padding: 12px 8px 8px 8px !important;
        justify-content: center !important;
    }

    /* 表格滚动提示 */
    .table-container::after {
        content: "← 左右滑动查看更多 →";
        display: block;
        text-align: center;
        font-size: 0.7rem;
        color: #6c757d;
        padding: 4px 0;
        background: #f8f9fa;
        border-top: 1px solid #dee2e6;
        margin-top: -1px;
    }



    /* 卡密号码显示优化 - 移动端完整显示 */
    .card-number-clickable {
        font-size: 0.7rem !important;
        word-break: break-all !important;
        max-width: none !important;
        display: inline-block !important;
        overflow: visible !important;
        text-overflow: unset !important;
        white-space: normal !important;
        line-height: 1.2 !important;
    }

    /* 内容标题显示优化 */
    .badge {
        font-size: 0.6rem !important;
        padding: 2px 4px !important;
        margin: 1px !important;
    }

    /* 状态开关优化 */
    .form-switch .form-check-input {
        width: 2rem !important;
        height: 1rem !important;
    }

    .form-switch .form-check-input::before {
        width: 0.75rem !important;
        height: 0.75rem !important;
        top: 1px !important;
        left: 1px !important;
    }

    .form-switch .form-check-input:checked::before {
        transform: translateX(1rem) !important;
    }

    /* 移动端菜单按钮优化 */
    .sidebar-toggle {
        display: block !important;
        background: none !important;
        border: none !important;
        color: var(--text-color) !important;
        font-size: 1.2rem !important;
        padding: 8px !important;
        border-radius: 4px !important;
        transition: background-color 0.2s ease !important;
    }

    .sidebar-toggle:hover {
        background-color: var(--bg-tertiary) !important;
    }

    /* 移动端头部优化 */
    .header-left {
        gap: 8px !important;
        flex: 1 !important;
    }

    .header-right {
        gap: 6px !important;
        flex-shrink: 0 !important;
    }

    /* 移动端页面标题优化 */
    .page-title {
        font-size: 1rem !important;
        margin: 0 !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        max-width: 200px !important;
    }

    /* 移动端用户信息优化 */
    .user-info-display {
        padding: 4px 8px !important;
    }

    .user-avatar-simple {
        width: 28px !important;
        height: 28px !important;
    }

    .user-details {
        display: none !important;
    }

    .user-action-link {
        width: 28px !important;
        height: 28px !important;
        font-size: 0.9rem !important;
    }

    /* 移动端卡片优化 */
    .card {
        margin-bottom: 12px !important;
        border-radius: 8px !important;
    }

    .card-body {
        padding: 12px !important;
    }

    /* 移动端按钮组优化 */
    .btn-group {
        flex-wrap: wrap !important;
        gap: 4px !important;
    }

    .btn-group .btn {
        flex: 1 1 auto !important;
        min-width: 0 !important;
    }

    /* 移动端输入组优化 */
    .input-group {
        flex-wrap: wrap !important;
    }

    .input-group .form-control {
        min-width: 0 !important;
        flex: 1 1 auto !important;
    }

    /* 移动端警告框优化 */
    .alert {
        font-size: 0.8rem !important;
        padding: 8px 12px !important;
        margin-bottom: 12px !important;
    }

    /* 移动端进度条优化 */
    .progress {
        height: 4px !important;
    }

    /* 移动端徽章优化 */
    .badge {
        font-size: 0.65rem !important;
        padding: 2px 6px !important;
    }

    /* 移动端工具提示优化 */
    .tooltip {
        font-size: 0.7rem !important;
    }

    /* 移动端下拉菜单优化 */
    .dropdown-menu {
        font-size: 0.8rem !important;
        min-width: 120px !important;
    }

    .dropdown-item {
        padding: 6px 12px !important;
        font-size: 0.8rem !important;
    }
}

/* 现代化开关按钮样式 */
.form-switch .form-check-input {
    width: 3rem !important;
    height: 1.5rem !important;
    border-radius: 0.75rem !important;
    background-color: #ccc !important;
    border: none !important;
    background-image: none !important;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1) !important;
    cursor: pointer !important;
    position: relative !important;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.form-switch .form-check-input:focus {
    border: none !important;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1), 0 0 0 3px rgba(var(--primary-rgb), 0.1) !important;
    background-color: #ccc !important;
}

.form-switch .form-check-input:checked {
    background-color: #28a745 !important;
    border: none !important;
    background-image: none !important;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.form-switch .form-check-input:checked:focus {
    border: none !important;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1), 0 0 0 3px rgba(40, 167, 69, 0.2) !important;
}

/* 开关按钮滑块样式 */
.form-switch .form-check-input::before {
    content: '' !important;
    position: absolute !important;
    top: 2px !important;
    left: 2px !important;
    width: 1.25rem !important;
    height: 1.25rem !important;
    background-color: #fff !important;
    border-radius: 50% !important;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2), 0 1px 2px rgba(0, 0, 0, 0.1) !important;
    z-index: 1 !important;
}

.form-switch .form-check-input:checked::before {
    transform: translateX(1.5rem) !important;
}

/* 开关按钮悬停效果 */
.form-switch .form-check-input:hover:not(:disabled) {
    background-color: #bbb !important;
}

.form-switch .form-check-input:checked:hover:not(:disabled) {
    background-color: #218838 !important;
}

.form-switch .form-check-input:hover:not(:disabled)::before {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.25), 0 2px 4px rgba(0, 0, 0, 0.15) !important;
}

/* 状态开关特定样式 */
.category-status-switch,
.content-status-switch {
    margin: 0 !important;
}

.form-switch {
    padding-left: 0 !important;
    min-height: auto !important;
}

.form-switch .form-check-input:disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
}

.form-switch .form-check-input:disabled::before {
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

/* 卡密设置页面样式 */
.card-settings-switch {
    transition: all 0.3s ease !important;
    border: 1px solid #e3e6f0 !important;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}

.card-settings-switch:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2) !important;
    border-color: var(--primary-color) !important;
}

.card-settings-switch .card-body {
    padding: 1.5rem !important;
}

.card-settings-switch .form-switch {
    padding-left: 0 !important;
    margin-bottom: 0 !important;
}

.card-settings-switch .form-switch .form-check-input {
    margin: 0 !important;
}

.card-settings-switch .card-title {
    color: #5a5c69 !important;
    font-weight: 600 !important;
    font-size: 0.9rem !important;
}

.card-settings-switch .card-text {
    font-size: 0.8rem !important;
    line-height: 1.4 !important;
    margin-bottom: 0 !important;
}

/* 卡密预览样式 */
#cardPreview {
    font-family: 'Courier New', monospace !important;
    font-size: 1.1rem !important;
    font-weight: bold !important;
    color: var(--primary-color) !important;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    padding: 0.75rem 1rem !important;
    border-radius: 0.375rem !important;
    border: 2px dashed var(--primary-color) !important;
    display: inline-block !important;
    min-width: 200px !important;
    text-align: center !important;
}

/* 功能开关区域标题 */
.settings-section-title {
    border-bottom: 2px solid var(--primary-color) !important;
    padding-bottom: 0.5rem !important;
    margin-bottom: 1.5rem !important;
}

.settings-section-title i {
    color: var(--primary-color) !important;
}

/* 可点击卡密样式 */
.card-number-clickable {
    transition: var(--transition);
    position: relative;
    cursor: pointer !important;
}

.card-number-clickable:hover {
    background-color: var(--primary-light) !important;
    color: var(--primary-color) !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    padding: 2px 4px;
}

.card-number-clickable:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 移动端最终优化 - 确保布局稳定 */
@media (max-width: 768px) {
    /* 防止内容溢出的全局样式 */
    * {
        box-sizing: border-box !important;
    }

    /* 防止水平滚动的容器 */
    .container,
    .container-fluid,
    .row,
    .col,
    [class*="col-"] {
        max-width: 100% !important;
        word-wrap: break-word !important;
        overflow-wrap: break-word !important;
    }

    /* 确保图片响应式 */
    img {
        max-width: 100% !important;
        height: auto !important;
    }

    /* 代码块响应式 */
    code,
    pre {
        word-wrap: break-word !important;
        overflow-wrap: break-word !important;
        white-space: pre-wrap !important;
        max-width: 100% !important;
    }

    /* 表格内容响应式 */
    .table td,
    .table th {
        word-wrap: break-word !important;
        overflow-wrap: break-word !important;
    }

    /* 长文本处理 */
    .text-truncate {
        max-width: 100% !important;
    }

    /* 确保模态框在移动端正确显示 */
    .modal-dialog {
        max-width: calc(100vw - 20px) !important;
        margin: 10px !important;
    }

    /* 生成卡密模态框 - 当前卡密生成设置紧凑显示 */
    #generateModal .card-body .row.g-2 {
        display: flex !important;
        flex-direction: column !important;
        gap: 1px !important;
        line-height: 1.2 !important;
    }

    #generateModal .card-body .row.g-2 .col-md-3,
    #generateModal .card-body .row.g-2 .col-md-6 {
        width: 100% !important;
        max-width: 100% !important;
        flex: 0 0 100% !important;
        padding: 1px 0 !important;
        margin-bottom: 0 !important;
        background: none !important;
        border: none !important;
        border-radius: 0 !important;
        line-height: 1.2 !important;
    }

    #generateModal .card-body .row.g-2 strong {
        font-weight: 500 !important;
        display: inline-block !important;
        min-width: 70px !important;
        font-size: 0.85rem !important;
        line-height: 1.2 !important;
    }

    #generateModal .card-body .row.g-2 span,
    #generateModal .card-body .row.g-2 code {
        font-weight: normal !important;
        font-size: 0.85rem !important;
        line-height: 1.2 !important;
    }

    /* 生成卡密模态框整体卡片内边距优化 */
    #generateModal .card.bg-light .card-body {
        padding: 8px 12px !important;
    }

    #generateModal .card.bg-light .card-title {
        margin-bottom: 8px !important;
        font-size: 0.9rem !important;
    }

    /* 确保下拉菜单在移动端正确显示 */
    .dropdown-menu {
        max-width: calc(100vw - 40px) !important;
        font-size: 0.8rem !important;
    }

    .dropdown-item {
        padding: 8px 12px !important;
        font-size: 0.8rem !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
    }

    /* 筛选模块卡片优化 */
    .card.mb-4 {
        margin-bottom: 8px !important;
    }

    .card .card-body {
        padding: 8px !important;
    }

    /* 确保筛选表单不会溢出 */
    .card .card-body .row.g-3 {
        max-width: 100% !important;
        overflow: hidden !important;
    }

    /* 表单控件最小高度统一 */
    .card .card-body .form-select-sm,
    .card .card-body .form-control-sm,
    .card .card-body .btn-sm {
        min-height: 28px !important;
        box-sizing: border-box !important;
    }

    /* 隐藏域处理 */
    .card .card-body input[type="hidden"] {
        display: none !important;
    }

    /* 筛选标签文字优化已在上面的响应式样式中定义 */

    /* 控制台页面特定优化 */
    /* 确保图表容器在移动端正确显示 */
    .chart-container {
        width: 100% !important;
        max-width: 100% !important;
        overflow: hidden !important;
    }

    /* 图表控制按钮优化 */
    .chart-controls {
        flex-wrap: wrap !important;
        gap: 4px !important;
    }

    .chart-controls .btn {
        font-size: 0.7rem !important;
        padding: 4px 8px !important;
    }

    /* 快捷操作标题优化 */
    .col-lg-4 .chart-header .chart-title {
        font-size: 1rem !important;
        margin-bottom: 8px !important;
    }

    /* 卡密使用趋势标题优化 */
    .col-lg-8 .chart-header .chart-title {
        font-size: 1rem !important;
    }

    /* Canvas图表容器优化 */
    .col-lg-8 .chart-container > div[style*="height"] {
        height: 250px !important;
    }

    /* 系统状态模块四个项目在一行显示 - 只针对col-12下的系统状态 */
    .col-12 .chart-container .row.g-3 {
        display: flex !important;
        flex-wrap: nowrap !important;
        gap: 4px !important;
        margin: 0 !important;
    }

    /* 移除冲突的样式，使用网格布局 */

    .col-12 .chart-container .row.g-3 .d-flex.align-items-center {
        padding: 6px 4px !important;
        flex-direction: column !important;
        text-align: center !important;
        align-items: center !important;
        justify-content: center !important;
    }

    .col-12 .chart-container .row.g-3 .me-3 {
        margin-right: 0 !important;
        margin-bottom: 2px !important;
    }

    .col-12 .chart-container .row.g-3 .fs-4 {
        font-size: 1rem !important;
    }

    .col-12 .chart-container .row.g-3 .fw-bold {
        font-size: 0.7rem !important;
        font-weight: 500 !important;
        margin-bottom: 1px !important;
        line-height: 1.1 !important;
    }

    .col-12 .chart-container .row.g-3 .text-success,
    .col-12 .chart-container .row.g-3 .text-warning,
    .col-12 .chart-container .row.g-3 .text-info {
        font-size: 0.65rem !important;
        line-height: 1 !important;
    }

    /* 控制台页面模块居中显示，左右间距一样 */
    .row .col-lg-4,
    .row .col-lg-8 {
        padding-left: 12px !important;
        padding-right: 12px !important;
    }

    /* 快捷操作模块内容居中 - 确保4个按钮都显示 */
    .col-lg-4 .chart-container {
        margin: 0 auto !important;
        max-width: 100% !important;
    }

    .col-lg-4 .chart-container .row.g-3 {
        justify-content: center !important;
        margin: 0 !important;
        display: flex !important;
        flex-wrap: wrap !important;
        gap: 8px !important;
    }

    .col-lg-4 .chart-container .col-6 {
        display: flex !important;
        justify-content: center !important;
        flex: 0 0 calc(50% - 4px) !important;
        max-width: calc(50% - 4px) !important;
    }

    .col-lg-4 .chart-container .btn {
        width: 100% !important;
        max-width: 200px !important;
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;
        padding: 12px 8px !important;
        min-height: 80px !important;
    }

    .col-lg-4 .chart-container .btn .fs-4 {
        margin-bottom: 4px !important;
    }

    /* 卡密使用趋势模块内容居中 */
    .col-lg-8 .chart-container {
        margin: 0 auto !important;
        max-width: 100% !important;
    }
}

/* 针对特定屏幕尺寸的微调 */
@media (max-width: 375px) {
    /* iPhone SE 等小屏幕设备优化 */
    .card .card-body .row.g-3 {
        gap: 1px !important;
    }

    /* 卡密管理页面 - 超小屏幕布局 */
    .card .card-body .col-md-3:nth-child(1) {
        flex: 0 0 16% !important;
        max-width: 16% !important;
    }

    .card .card-body .col-md-6:nth-child(2) {
        flex: 1 1 54% !important;
        max-width: 54% !important;
    }

    .card .card-body .col-md-3:nth-child(3) {
        flex: 0 0 30% !important;
        max-width: 30% !important;
    }

    /* 内容管理页面 - 超小屏幕布局 */
    .card .card-body .col-md-3:nth-child(2) {
        flex: 0 0 20% !important;
        max-width: 20% !important;
    }

    .card .card-body .col-md-2:nth-child(3) {
        flex: 0 0 12% !important;
        max-width: 12% !important;
    }

    .card .card-body .col-md-4:nth-child(4) {
        flex: 1 1 43% !important;
        max-width: 43% !important;
    }

    .card .card-body .col-md-3:nth-child(5) {
        flex: 0 0 25% !important;
        max-width: 25% !important;
    }

    /* 表单控件进一步缩小 */
    .card .card-body .form-label.small {
        font-size: 0.55rem !important;
        height: 24px !important;
    }

    .card .card-body .form-select-sm,
    .card .card-body .form-control-sm {
        font-size: 0.6rem !important;
        padding: 1px 2px !important;
        height: 24px !important;
    }

    .card .card-body .btn-sm {
        font-size: 0.55rem !important;
        padding: 1px 3px !important;
        height: 11px !important;
        line-height: 1 !important;
    }

    /* 筛选项容器高度调整 */
    .card .card-body .row.align-items-center.g-1 {
        min-height: 24px !important;
    }



    .stat-card .stat-header {
        margin-bottom: 0px !important;
    }

    .stat-card .stat-title {
        font-size: 0.55rem !important;
        line-height: 1 !important;
        margin-bottom: 1px !important;
    }

    .stat-card .stat-value {
        font-size: 0.8rem !important;
        line-height: 1 !important;
        margin-bottom: 0px !important;
    }

    .stat-card .stat-change {
        font-size: 0.5rem !important;
        line-height: 1 !important;
    }

    /* 快捷操作按钮超小屏幕优化 - 确保4个按钮都显示 */
    .col-lg-4 .chart-container .row.g-3 {
        gap: 4px !important;
    }

    .col-lg-4 .chart-container .col-6 {
        flex: 0 0 calc(50% - 2px) !important;
        max-width: calc(50% - 2px) !important;
    }

    .col-lg-4 .chart-container .btn {
        padding: 8px 4px !important;
        font-size: 0.7rem !important;
        min-height: 60px !important;
    }

    .col-lg-4 .chart-container .btn .fs-4 {
        font-size: 0.9rem !important;
        margin-bottom: 2px !important;
    }

    /* 375px以下 - 卡密设置和前端设置页面最紧凑优化 */
    .tab-pane#popup .row.g-3 {
        gap: 4px !important;
    }

    .tab-pane#popup .row.align-items-center.g-2,
    .tab-pane#popup .row.align-items-start.g-2 {
        gap: 2px !important;
        margin-bottom: 4px !important;
    }

    .tab-pane#popup .col-auto {
        min-width: 60px !important;
        padding-right: 4px !important;
    }

    .tab-pane#popup .form-label {
        font-size: 0.75rem !important;
    }

    .tab-pane#popup .text-muted {
        font-size: 0.65rem !important;
    }

    .tab-pane#popup .alert {
        padding: 4px 8px !important;
        margin-top: 4px !important;
    }

    .tab-pane#popup .alert .alert-heading {
        font-size: 0.75rem !important;
        margin-bottom: 2px !important;
    }

    .tab-pane#popup .alert li {
        font-size: 0.65rem !important;
        margin-bottom: 0px !important;
    }

    /* 前端设置页面最紧凑优化 */
    .tab-pane .row.align-items-center.g-2,
    .tab-pane .row.align-items-start.g-2 {
        gap: 2px !important;
        margin-bottom: 3px !important;
    }

    .tab-pane .col-auto {
        min-width: 60px !important;
        padding-right: 3px !important;
    }

    .tab-pane .form-label {
        font-size: 0.7rem !important;
    }

    .tab-pane .text-muted {
        font-size: 0.6rem !important;
    }

    .tab-pane textarea.form-control,
    .tab-pane input.form-control {
        font-size: 0.7rem !important;
        padding: 3px 5px !important;
    }

    /* 生成卡密模态框超小屏幕优化 */
    #generateModal .modal-dialog {
        margin: 5px !important;
        max-width: calc(100vw - 10px) !important;
    }

    #generateModal .card-body .row.g-2 {
        gap: 0px !important;
        line-height: 1 !important;
    }

    #generateModal .card-body .row.g-2 .col-md-3,
    #generateModal .card-body .row.g-2 .col-md-6 {
        padding: 0px 0 !important;
        margin-bottom: 0 !important;
        line-height: 1 !important;
    }

    #generateModal .card-body .row.g-2 strong {
        min-width: 50px !important;
        font-size: 0.75rem !important;
        line-height: 1 !important;
    }

    #generateModal .card-body .row.g-2 span,
    #generateModal .card-body .row.g-2 code {
        font-size: 0.75rem !important;
        line-height: 1 !important;
    }

    /* 375px以下卡片内边距最紧凑优化 */
    #generateModal .card.bg-light .card-body {
        padding: 4px 8px !important;
    }

    #generateModal .card.bg-light .card-title {
        margin-bottom: 4px !important;
        font-size: 0.8rem !important;
    }

    /* 卡密设置页面弹窗设置优化 - 减少内容间距 */
    .tab-pane#popup .row.g-3 {
        gap: 8px !important;
    }

    .tab-pane#popup .col-12 {
        margin-bottom: 0 !important;
    }

    .tab-pane#popup .row.align-items-center.g-2,
    .tab-pane#popup .row.align-items-start.g-2 {
        gap: 4px !important;
        margin-bottom: 8px !important;
    }

    .tab-pane#popup .col-auto {
        padding-right: 8px !important;
        min-width: 80px !important;
    }

    .tab-pane#popup .col-sm-6 {
        flex: 1 1 auto !important;
        max-width: none !important;
    }

    .tab-pane#popup .col {
        padding-left: 8px !important;
    }

    .tab-pane#popup .form-label {
        margin-bottom: 0 !important;
        font-size: 0.85rem !important;
    }

    .tab-pane#popup .text-muted {
        font-size: 0.75rem !important;
        line-height: 1.2 !important;
    }

    .tab-pane#popup .alert {
        margin-top: 8px !important;
        margin-bottom: 8px !important;
        padding: 8px 12px !important;
    }

    .tab-pane#popup .alert .alert-heading {
        font-size: 0.85rem !important;
        margin-bottom: 4px !important;
    }

    .tab-pane#popup .alert ul {
        margin-bottom: 0 !important;
    }

    .tab-pane#popup .alert li {
        margin-bottom: 2px !important;
        font-size: 0.75rem !important;
    }

    /* 前端设置页面通知弹窗优化 - 100%宽度显示 */
    .tab-pane .row.g-1 .col-9 {
        flex: 1 1 100% !important;
        max-width: 100% !important;
        width: 100% !important;
    }

    .tab-pane .row.g-1 .col-9 .row.g-2 {
        margin: 0 !important;
    }

    .tab-pane .row.g-1 .col-9 .col-7 {
        flex: 1 1 100% !important;
        max-width: 100% !important;
        width: 100% !important;
        padding: 0 !important;
    }

    .tab-pane .row.g-1 .col-9 .col-5 {
        flex: 1 1 100% !important;
        max-width: 100% !important;
        width: 100% !important;
        padding: 0 !important;
        margin-top: 8px !important;
    }

    /* 前端设置页面弹窗提示优化 - 减少内容间距 */
    .tab-pane .row.align-items-center.g-2 {
        gap: 4px !important;
        margin-bottom: 6px !important;
    }

    .tab-pane .row.align-items-start.g-2 {
        gap: 4px !important;
        margin-bottom: 6px !important;
    }

    .tab-pane .col-auto {
        padding-right: 6px !important;
        min-width: 80px !important;
    }

    .tab-pane .col-sm-6 {
        flex: 1 1 auto !important;
        max-width: none !important;
    }

    .tab-pane .col {
        padding-left: 6px !important;
    }

    .tab-pane .form-label {
        margin-bottom: 0 !important;
        font-size: 0.8rem !important;
    }

    .tab-pane .text-muted {
        font-size: 0.7rem !important;
        line-height: 1.2 !important;
    }

    .tab-pane textarea.form-control {
        font-size: 0.8rem !important;
        padding: 6px 8px !important;
    }

    .tab-pane input.form-control {
        font-size: 0.8rem !important;
        padding: 6px 8px !important;
    }

    /* 前端设置页面格式化按钮组优化 */
    .tab-pane .btn-group {
        flex-wrap: wrap !important;
        gap: 2px !important;
    }

    .tab-pane .btn-group .btn {
        font-size: 0.7rem !important;
        padding: 4px 6px !important;
        margin-bottom: 2px !important;
    }

    .tab-pane .btn-group-sm .btn {
        font-size: 0.65rem !important;
        padding: 3px 5px !important;
    }

    .tab-pane .dropdown-menu {
        font-size: 0.75rem !important;
    }

    .tab-pane .dropdown-item {
        padding: 4px 8px !important;
        font-size: 0.75rem !important;
    }

    /* 预览按钮优化 */
    .tab-pane .btn-outline-info {
        font-size: 0.8rem !important;
        padding: 6px 12px !important;
    }

    /* 控制台页面模块超小屏幕优化 */
    .row .col-lg-4,
    .row .col-lg-8 {
        padding-left: 6px !important;
        padding-right: 6px !important;
    }

    .col-lg-4 .chart-container .btn {
        max-width: 160px !important;
        font-size: 0.75rem !important;
        padding: 5px !important;
    }

    .col-lg-4 .chart-container .btn .fs-4 {
        font-size: 0.9rem !important;
    }

    /* 系统状态模块375px以下最紧凑优化 - 只针对col-12 */
    .col-12 .chart-container .row.g-3 {
        gap: 1px !important;
    }

    .col-12 .chart-container .row.g-3 .d-flex.align-items-center {
        padding: 3px 1px !important;
    }

    .col-12 .chart-container .row.g-3 .me-3 {
        margin-bottom: 0px !important;
    }

    .col-12 .chart-container .row.g-3 .fs-4 {
        font-size: 0.8rem !important;
    }

    .col-12 .chart-container .row.g-3 .fw-bold {
        font-size: 0.6rem !important;
        margin-bottom: 0px !important;
        line-height: 1 !important;
    }

    .col-12 .chart-container .row.g-3 .text-success,
    .col-12 .chart-container .row.g-3 .text-warning,
    .col-12 .chart-container .row.g-3 .text-info {
        font-size: 0.55rem !important;
        line-height: 1 !important;
    }

    /* 生成卡密模态框进一步紧凑优化 */
    #generateModal .card-body .row.g-2 {
        gap: 0px !important;
    }

    #generateModal .card-body .row.g-2 .col-md-3,
    #generateModal .card-body .row.g-2 .col-md-6 {
        padding: 0px 0 !important;
        margin-bottom: 0 !important;
        line-height: 1.1 !important;
    }

    #generateModal .card-body .row.g-2 strong {
        min-width: 60px !important;
        font-size: 0.8rem !important;
        line-height: 1.1 !important;
    }

    #generateModal .card-body .row.g-2 span,
    #generateModal .card-body .row.g-2 code {
        font-size: 0.8rem !important;
        line-height: 1.1 !important;
    }

    /* 480px以下卡片内边距进一步优化 */
    #generateModal .card.bg-light .card-body {
        padding: 6px 10px !important;
    }

    #generateModal .card.bg-light .card-title {
        margin-bottom: 6px !important;
        font-size: 0.85rem !important;
    }

    /* 控制台页面模块间距进一步优化 */
    .row .col-lg-4,
    .row .col-lg-8 {
        padding-left: 8px !important;
        padding-right: 8px !important;
    }

    .col-lg-4 .chart-container .btn {
        max-width: 180px !important;
        font-size: 0.8rem !important;
        padding: 6px !important;
    }

    /* 系统状态模块480px以下优化 - 只针对col-12 */
    .col-12 .chart-container .row.g-3 {
        gap: 2px !important;
    }

    .col-12 .chart-container .row.g-3 .d-flex.align-items-center {
        padding: 4px 2px !important;
    }

    .col-12 .chart-container .row.g-3 .me-3 {
        margin-bottom: 1px !important;
    }

    .col-12 .chart-container .row.g-3 .fs-4 {
        font-size: 0.9rem !important;
    }

    .col-12 .chart-container .row.g-3 .fw-bold {
        font-size: 0.65rem !important;
        margin-bottom: 0px !important;
        line-height: 1 !important;
    }

    .col-12 .chart-container .row.g-3 .text-success,
    .col-12 .chart-container .row.g-3 .text-warning,
    .col-12 .chart-container .row.g-3 .text-info {
        font-size: 0.6rem !important;
        line-height: 1 !important;
    }
}
