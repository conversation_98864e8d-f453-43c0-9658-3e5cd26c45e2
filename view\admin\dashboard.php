<?php
$page_title = '控制台';
$current_page = 'dashboard';

// 使用从控制器传递的真实统计数据

$content = '
<!-- 统计卡片 -->
<div class="stats-grid">
    <div class="stat-card primary">
        <div class="stat-header">
            <h6 class="stat-title">卡密总数</h6>
            <div class="stat-icon primary">
                <i class="fas fa-credit-card"></i>
            </div>
        </div>
        <div class="stat-value">' . number_format($stats['total_cards']) . '</div>
        <div class="stat-change positive">
            <i class="fas fa-arrow-up"></i>
            <span>↑ 12% 较上月</span>
        </div>
    </div>
    
    <div class="stat-card success">
        <div class="stat-header">
            <h6 class="stat-title">已使用卡密</h6>
            <div class="stat-icon success">
                <i class="fas fa-check-circle"></i>
            </div>
        </div>
        <div class="stat-value">' . number_format($stats['used_cards']) . '</div>
        <div class="stat-change positive">
            <i class="fas fa-arrow-up"></i>
            <span>↑ 8% 较上月</span>
        </div>
    </div>
    
    <div class="stat-card warning">
        <div class="stat-header">
            <h6 class="stat-title">未使用卡密</h6>
            <div class="stat-icon warning">
                <i class="fas fa-clock"></i>
            </div>
        </div>
        <div class="stat-value">' . number_format($stats['unused_cards']) . '</div>
        <div class="stat-change positive">
            <i class="fas fa-arrow-up"></i>
            <span>↑ 21% 较上月</span>
        </div>
    </div>
    
    <div class="stat-card info">
        <div class="stat-header">
            <h6 class="stat-title">总兑换次数</h6>
            <div class="stat-icon info">
                <i class="fas fa-exchange-alt"></i>
            </div>
        </div>
        <div class="stat-value">' . number_format($stats['total_exchanges']) . '</div>
        <div class="stat-change positive">
            <i class="fas fa-arrow-up"></i>
            <span>今日 ' . $stats['today_exchanges'] . ' 次</span>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row">
    <div class="col-lg-8">
        <div class="chart-container">
            <div class="chart-header">
                <h5 class="chart-title">卡密使用趋势</h5>
                <div class="chart-controls">
                    <button class="btn btn-outline-primary btn-sm active" data-period="7">7天</button>
                    <button class="btn btn-outline-primary btn-sm" data-period="30">30天</button>
                    <button class="btn btn-outline-primary btn-sm" data-period="90">90天</button>
                </div>
            </div>
            <div style="height: 300px;">
                <canvas id="usageChart"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="chart-container">
            <div class="chart-header">
                <h5 class="chart-title">快捷操作</h5>
            </div>
            <div class="row g-3 p-3">
                <div class="col-6">
                    <button type="button" class="btn btn-primary w-100 p-3" data-bs-toggle="modal" data-bs-target="#generateModal">
                        <i class="fas fa-plus-circle fs-4 d-block mb-2"></i>
                        生成卡密
                    </button>
                </div>
                <div class="col-6">
                    <a href="/admin/categories" class="btn btn-outline-primary w-100 p-3">
                        <i class="fas fa-folder-plus fs-4 d-block mb-2"></i>
                        分类管理
                    </a>
                </div>
                <div class="col-6">
                    <a href="/admin/contents" class="btn btn-outline-primary w-100 p-3">
                        <i class="fas fa-file-alt fs-4 d-block mb-2"></i>
                        内容管理
                    </a>
                </div>
                <div class="col-6">
                    <a href="/admin/settings" class="btn btn-outline-primary w-100 p-3">
                        <i class="fas fa-cog fs-4 d-block mb-2"></i>
                        系统设置
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近活动 -->
<div class="chart-container">
    <div class="chart-header">
        <h5 class="chart-title">最近活动</h5>
        <a href="/admin/cards" class="btn btn-outline-primary btn-sm">查看全部</a>
    </div>

    <div class="table-container">
        <table class="table">
            <thead>
                <tr>
                    <th>卡密</th>
                    <th>操作类型</th>
                    <th>操作时间</th>
                    <th>使用状态</th>
                    <th>启用状态</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>';

// 动态生成最近活动数据
if (!empty($stats['recent_activities'])) {
    foreach ($stats['recent_activities'] as $activity) {
        $actionTime = date('Y-m-d H:i', strtotime($activity['action_time']));

        // 根据使用状态设置标签 - 只有已使用和未使用两种状态
        $useStatusBadge = '';
        switch ($activity['use_status']) {
            case '未使用':
                $useStatusBadge = '<span class="status-badge warning">未使用</span>';
                break;
            case '已使用':
                $useStatusBadge = '<span class="status-badge success">已使用</span>';
                break;
            default:
                $useStatusBadge = '<span class="status-badge secondary">未知</span>';
        }

        // 根据启用状态设置标签 - 已禁用、已启用、已删除三种状态
        $enableStatusBadge = '';
        switch ($activity['enable_status']) {
            case '已启用':
                $enableStatusBadge = '<span class="status-badge success">已启用</span>';
                break;
            case '已禁用':
                $enableStatusBadge = '<span class="status-badge danger">已禁用</span>';
                break;
            case '已删除':
                $enableStatusBadge = '<span class="status-badge dark">已删除</span>';
                break;
            default:
                $enableStatusBadge = '<span class="status-badge secondary">未知</span>';
        }

        // 操作按钮
        $actionButton = '';
        if ($activity['card_id']) {
            $actionButton = '<button class="btn btn-outline-primary btn-sm" onclick="viewCardDetail(' . $activity['card_id'] . ')">
                                <i class="fas fa-eye"></i> 查看
                            </button>';
        } else {
            $actionButton = '<span class="text-muted">-</span>';
        }

        // 如果卡密已删除，添加删除样式
        $rowClass = $activity['is_deleted'] ? 'class="deleted-row"' : '';

        $content .= '
                <tr ' . $rowClass . '>
                    <td><code class="card-number-clickable" onclick="copyCardNumberFromTable(\'' . htmlspecialchars($activity['card_number']) . '\')" title="点击复制卡密">' . htmlspecialchars($activity['card_number']) . '</code></td>
                    <td>' . htmlspecialchars($activity['action']) . '</td>
                    <td>' . $actionTime . '</td>
                    <td>' . $useStatusBadge . '</td>
                    <td>' . $enableStatusBadge . '</td>
                    <td>' . $actionButton . '</td>
                </tr>';
    }
} else {
    $content .= '
                <tr>
                    <td colspan="6" class="text-center text-muted">暂无活动记录</td>
                </tr>';
}

$content .= '
            </tbody>
        </table>
    </div>
</div>



<script>
// HTML转义函数，防止XSS攻击
function escapeHtml(text) {
    const div = document.createElement(\'div\');
    div.textContent = text;
    return div.innerHTML;
}

// 图表初始化
document.addEventListener("DOMContentLoaded", function() {
    // 使用趋势图表
    const usageCtx = document.getElementById("usageChart").getContext("2d");
    let usageChart = new Chart(usageCtx, {
        type: "line",
        data: {
            labels: [],
            datasets: [{
                label: "兑换次数",
                data: [],
                borderColor: "rgb(79, 70, 229)",
                backgroundColor: "rgba(79, 70, 229, 0.1)",
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: "rgba(0, 0, 0, 0.1)"
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });

    // 加载使用趋势数据
    function loadUsageTrend(period = 7) {
        fetch("/admin/getUsageTrend", {
            method: "POST",
            headers: {
                "Content-Type": "application/x-www-form-urlencoded",
            },
            body: `period=${period}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 1) {
                usageChart.data.labels = data.data.labels;
                usageChart.data.datasets[0].data = data.data.data;
                usageChart.update();
            } else {
                console.error("获取趋势数据失败:", data.msg);
            }
        })
        .catch(error => {
            console.error("网络错误:", error);
        });
    }

    // 绑定时间段按钮事件
    document.querySelectorAll("[data-period]").forEach(button => {
        button.addEventListener("click", function() {
            const period = parseInt(this.getAttribute("data-period"));

            // 更新按钮状态
            document.querySelectorAll("[data-period]").forEach(btn => {
                btn.classList.remove("active");
            });
            this.classList.add("active");

            // 加载对应时间段的数据
            loadUsageTrend(period);
        });
    });

    // 初始加载7天数据
    loadUsageTrend(7);

});

// 查看卡密详情
function viewCardDetail(cardId) {
    fetch("/admin/getCardDetail", {
        method: "POST",
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
        },
        body: "card_id=" + cardId
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            showCardDetailModal(data.data);
        } else {
            AdminUtils.showMessage(data.msg || "获取详情失败", \'error\');
        }
    })
    .catch(error => {
        console.error("获取详情错误:", error);
        AdminUtils.showMessage("网络错误", \'error\');
    });
}

// 显示卡密详情弹窗
function showCardDetailModal(cardData) {
    // 填充卡密号码
    document.getElementById(\'detail-card-number\').textContent = cardData.card_number;

    // 填充内容标题
    const contentList = document.getElementById(\'detail-content-list\');
    if (cardData.contents && cardData.contents.length > 0) {
        let contentHtml = \'\';
        cardData.contents.forEach((content, index) => {
            if (index > 0) contentHtml += \'<br>\';
            contentHtml += \'<span class="badge bg-primary me-1">\' + escapeHtml(content.title) + \'</span>\';
        });
        contentList.innerHTML = contentHtml;
    } else {
        contentList.innerHTML = \'<span class="text-muted">暂无关联内容</span>\';
    }

    // 填充使用状态
    document.getElementById(\'detail-use-status\').innerHTML = getStatusBadge(cardData.status, cardData.status_text);

    // 填充时间信息
    document.getElementById(\'detail-create-time\').textContent = cardData.create_time;
    document.getElementById(\'detail-used-time\').textContent = cardData.used_time ? cardData.used_time : \'-\';
    document.getElementById(\'detail-used-ip\').textContent = cardData.used_ip || \'-\';

    // 处理过期时间显示
    const expireTimeElement = document.getElementById(\'detail-expire-time\');
    if (!cardData.expire_time || cardData.expire_time === \'0000-00-00 00:00:00\' || cardData.expire_time === null) {
        expireTimeElement.innerHTML = \'<span class="text-success fw-bold">永久有效</span>\';
    } else {
        const expireDate = new Date(cardData.expire_time);
        const now = new Date();

        if (expireDate > now) {
            expireTimeElement.textContent = cardData.expire_time;
            expireTimeElement.className = \'text-muted\';
        } else {
            expireTimeElement.innerHTML = \'<span class="text-danger">\' + escapeHtml(cardData.expire_time) + \' (已过期)</span>\';
        }
    }

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById(\'cardDetailsModal\'));
    modal.show();
}

// 生成状态标签
function getStatusBadge(status, statusText) {
    let badgeClass = "secondary";
    switch (status) {
        case 0: badgeClass = "danger"; break;
        case 1: badgeClass = "warning"; break;
        case 2: badgeClass = "success"; break;
    }
    return `<span class="badge bg-${badgeClass}">${statusText}</span>`;
}

// 格式化过期时间
function formatExpireTime(expireTime) {
    if (!expireTime || expireTime === "0000-00-00 00:00:00" || expireTime === null) {
        return \'<span class="text-success fw-bold">永久有效</span>\';
    }

    const expireDate = new Date(expireTime);
    const now = new Date();

    if (expireDate > now) {
        return expireTime;
    } else {
        return \'<span class="text-danger">\' + expireTime + \' (已过期)</span>\';
    }
}

// 生成内容部分
function generateContentsSection(contents) {
    if (!contents || contents.length === 0) {
        return "";
    }

    let html = `<div class="mt-3"><h6>关联内容:</h6><ul class="list-group">`;
    contents.forEach(content => {
        html += `<li class="list-group-item">${content.title}</li>`;
    });
    html += `</ul></div>`;
    return html;
}

// 生成兑换记录部分
function generateExchangeRecordsSection(records) {
    if (!records || records.length === 0) {
        return "";
    }

    let html = `<div class="mt-3"><h6>兑换记录:</h6><div class="table-responsive"><table class="table table-sm">`;
    html += `<thead><tr><th>兑换时间</th><th>IP地址</th><th>状态</th></tr></thead><tbody>`;
    records.forEach(record => {
        const statusBadge = record.status === 1 ?
            `<span class="badge bg-success">成功</span>` :
            `<span class="badge bg-danger">失败</span>`;
        html += `<tr><td>${record.exchange_time}</td><td>${record.exchange_ip || "-"}</td><td>${statusBadge}</td></tr>`;
    });
    html += `</tbody></table></div></div>`;
    return html;
}

// 复制卡密号码
function copyCardNumber() {
    const cardNumber = document.getElementById(\'detail-card-number\').textContent;
    if (navigator.clipboard) {
        navigator.clipboard.writeText(cardNumber).then(() => {
            AdminUtils.showMessage(\'卡密已复制到剪贴板\', \'success\');
        }).catch(() => {
            fallbackCopyTextToClipboard(cardNumber);
        });
    } else {
        fallbackCopyTextToClipboard(cardNumber);
    }
}

// 备用复制方法
function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        document.execCommand(\'copy\');
        AdminUtils.showMessage(\'卡密已复制到剪贴板\', \'success\');
    } catch (err) {
        AdminUtils.showMessage(\'复制失败，请手动复制\', \'error\');
    }

    document.body.removeChild(textArea);
}

// 从表格中复制卡密号码
function copyCardNumberFromTable(cardNumber) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(cardNumber).then(() => {
            showCopySuccess();
        }).catch(() => {
            fallbackCopyTextToClipboard(cardNumber);
        });
    } else {
        fallbackCopyTextToClipboard(cardNumber);
    }
}

// 显示复制成功提示
function showCopySuccess() {
    // 创建临时提示元素
    const toast = document.createElement(\'div\');
    toast.className = \'copy-success-toast\';
    toast.textContent = \'卡密已复制到剪贴板\';
    document.body.appendChild(toast);

    // 显示动画
    setTimeout(() => {
        toast.classList.add(\'show\');
    }, 10);

    // 3秒后移除
    setTimeout(() => {
        toast.classList.remove(\'show\');
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, 2000);
}
</script>

<style>
/* 已删除卡密行样式 - 仅应用于表格行 */
tr.deleted-row {
    background-color: #f8d7da !important;
    color: #721c24 !important;
    opacity: 0.8;
}

tr.deleted-row td {
    text-decoration: line-through;
    text-decoration-color: #dc3545;
    text-decoration-thickness: 1px;
}

/* 为已删除行添加轻微的边框提示 */
tr.deleted-row {
    border-left: 3px solid #dc3545 !important;
}

/* 可点击的卡密号码样式 */
.card-number-clickable {
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 2px 4px;
    border-radius: 3px;
}

.card-number-clickable:hover {
    background-color: #e3f2fd;
    color: #1976d2 !important;
    transform: scale(1.02);
}

/* 复制成功提示样式 */
.copy-success-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: #28a745;
    color: white;
    padding: 12px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 9999;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
}

.copy-success-toast.show {
    opacity: 1;
    transform: translateX(0);
}
</style>

<!-- 卡密详情模态框 -->
<div class="modal fade" id="cardDetailsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">卡密详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="card">
                    <div class="card-body">
                        <!-- 卡密号码 -->
                        <div class="mb-3 pb-2 border-bottom card-detail-item">
                            <div class="row align-items-center">
                                <div class="col-3 card-detail-label">
                                    <strong>卡密号码：</strong>
                                </div>
                                <div class="col-9 card-detail-value">
                                    <div class="d-flex align-items-center">
                                        <span id="detail-card-number" class="text-primary me-2 card-number-clickable" onclick="copyCardNumber()" title="点击复制卡密" style="cursor: pointer;"></span>
                                        <i class="fas fa-copy text-muted" style="font-size: 0.8rem;" title="点击复制"></i>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 内容标题 -->
                        <div class="mb-3 pb-2 border-bottom card-detail-item">
                            <div class="row">
                                <div class="col-3 card-detail-label">
                                    <strong>内容标题：</strong>
                                </div>
                                <div class="col-9 card-detail-value" id="detail-content-list">
                                    <!-- 关联内容列表将在这里显示 -->
                                </div>
                            </div>
                        </div>

                        <!-- 使用状态 -->
                        <div class="mb-3 pb-2 border-bottom card-detail-item">
                            <div class="row align-items-center">
                                <div class="col-3 card-detail-label">
                                    <strong>使用状态：</strong>
                                </div>
                                <div class="col-9 card-detail-value">
                                    <span id="detail-use-status"></span>
                                </div>
                            </div>
                        </div>

                        <!-- 生成时间 -->
                        <div class="mb-3 pb-2 border-bottom card-detail-item">
                            <div class="row align-items-center">
                                <div class="col-3 card-detail-label">
                                    <strong>生成时间：</strong>
                                </div>
                                <div class="col-9 card-detail-value">
                                    <span id="detail-create-time" class="text-muted"></span>
                                </div>
                            </div>
                        </div>

                        <!-- 使用时间 -->
                        <div class="mb-3 pb-2 border-bottom card-detail-item">
                            <div class="row align-items-center">
                                <div class="col-3 card-detail-label">
                                    <strong>使用时间：</strong>
                                </div>
                                <div class="col-9 card-detail-value">
                                    <span id="detail-used-time" class="text-muted"></span>
                                </div>
                            </div>
                        </div>

                        <!-- 使用者IP -->
                        <div class="mb-3 pb-2 border-bottom card-detail-item">
                            <div class="row align-items-center">
                                <div class="col-3 card-detail-label">
                                    <strong>使用者IP：</strong>
                                </div>
                                <div class="col-9 card-detail-value">
                                    <span id="detail-used-ip" class="text-muted"></span>
                                </div>
                            </div>
                        </div>

                        <!-- 过期时间 -->
                        <div class="mb-3 card-detail-item">
                            <div class="row align-items-center">
                                <div class="col-3 card-detail-label">
                                    <strong>过期时间：</strong>
                                </div>
                                <div class="col-9 card-detail-value">
                                    <span id="detail-expire-time" class="text-muted"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 生成卡密模态框 -->
<div class="modal fade" id="generateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus-circle me-2"></i>生成卡密
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="generateForm">
                <div class="modal-body">
                    <div class="row g-4">
                        <!-- 分级分类选择 -->
                        <div class="col-12">
                            <label class="form-label">
                                <i class="fas fa-folder me-2"></i>选择分类
                            </label>

                            <!-- 一级分类选择 -->
                            <div class="mb-3">
                                <select class="form-select" id="level1Select" required>
                                    <option value="">请选择一级分类</option>
                                </select>
                            </div>

                            <!-- 二级分类选择 -->
                            <div class="mb-3" id="level2Container" style="display: none;">
                                <select class="form-select" id="level2Select">
                                    <option value="">请选择二级分类</option>
                                </select>
                            </div>

                            <!-- 三级分类选择 -->
                            <div class="mb-3" id="level3Container" style="display: none;">
                                <select class="form-select" id="level3Select">
                                    <option value="">请选择三级分类</option>
                                </select>
                            </div>

                            <!-- 隐藏字段存储最终选择的分类ID -->
                            <input type="hidden" name="category_id" id="finalCategoryId">
                        </div>

                        <!-- 第二行：选择内容 -->
                        <div class="col-12">
                            <label class="form-label">
                                <i class="fas fa-file-alt me-2"></i>选择该分类下的内容
                            </label>
                            <div id="contentContainer" class="border rounded p-3 text-start" style="min-height: 120px; max-height: 200px; overflow-y: auto;">
                                <div class="text-center text-muted py-3">
                                    <i class="fas fa-info-circle me-2"></i>请先选择分类
                                </div>
                            </div>
                        </div>

                        <!-- 卡密设置信息显示 -->
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title mb-3">
                                        <i class="fas fa-cog me-2"></i>当前卡密生成设置
                                    </h6>
                                    <div class="row g-2 small">
                                        <div class="col-md-3">
                                            <strong>生成数量：</strong><span id="settingCount">1</span>个
                                        </div>
                                        <div class="col-md-3">
                                            <strong>卡密长度：</strong><span id="settingLength">8</span>位
                                        </div>
                                        <div class="col-md-3">
                                            <strong>字符组合：</strong><span id="settingCharType">数字+字母</span>
                                        </div>
                                        <div class="col-md-3">
                                            <strong>使用次数：</strong><span id="settingUsageLimit">1</span>次
                                        </div>
                                        <div class="col-md-3">
                                            <strong>卡密前缀：</strong><span id="settingPrefix">HZO-</span>
                                        </div>
                                        <div class="col-md-3">
                                            <strong>有效期：</strong><span id="settingValidity">永不过期</span>
                                        </div>
                                        <div class="col-md-6">
                                            <strong>预览格式：</strong><code id="settingPreview">HZO-A1B2C3D4</code>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>取消
                    </button>
                    <button type="submit" class="btn btn-primary" id="generateBtn" disabled>
                        <i class="fas fa-magic me-2"></i>生成卡密
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 生成成功模态框 -->
<div class="modal fade" id="cardSuccessModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle me-2"></i>卡密生成成功
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="successContent">
                    <!-- 成功内容将通过JavaScript动态填充 -->
                </div>
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-primary" onclick="copySuccessContent()">
                    <i class="fas fa-copy me-2"></i>复制内容
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// 页面加载完成后初始化
document.addEventListener("DOMContentLoaded", function() {
    loadCardSettings();
    initCategorySelects();
});

// 初始化分类选择器
function initCategorySelects() {
    // 加载一级分类
    fetch("/admin/getCategoriesByLevel?level=1")
        .then(response => response.json())
        .then(data => {
            console.log("一级分类API响应:", data);
            const level1Select = document.getElementById("level1Select");
            level1Select.innerHTML = "<option value=\"\">请选择一级分类</option>";

            if (data.code === 1 && data.data) {
                data.data.forEach(category => {
                    level1Select.innerHTML += `<option value="${category.id}">${category.name}</option>`;
                });
            } else {
                console.error("获取一级分类失败:", data);
            }
        })
        .catch(error => {
            console.error("获取一级分类错误:", error);
        });

    // 一级分类变化事件
    document.getElementById("level1Select").addEventListener("change", function() {
        const level1Id = this.value;
        const level2Container = document.getElementById("level2Container");
        const level3Container = document.getElementById("level3Container");

        // 重置下级分类
        level2Container.style.display = "none";
        level3Container.style.display = "none";
        document.getElementById("level2Select").innerHTML = "<option value=\"\">请选择二级分类</option>";
        document.getElementById("level3Select").innerHTML = "<option value=\"\">请选择三级分类</option>";

        if (level1Id) {
            // 设置最终分类ID
            document.getElementById("finalCategoryId").value = level1Id;

            // 加载该分类的内容
            loadCategoryContents(level1Id);

            // 加载二级分类
            fetch(`/admin/getCategoriesByLevel?parent_id=${level1Id}`)
                .then(response => response.json())
                .then(data => {
                    console.log("二级分类API响应:", data);
                    if (data.code === 1 && data.data && data.data.length > 0) {
                        level2Container.style.display = "block";
                        const level2Select = document.getElementById("level2Select");
                        data.data.forEach(category => {
                            level2Select.innerHTML += `<option value="${category.id}">${category.name}</option>`;
                        });
                    }
                })
                .catch(error => {
                    console.error("获取二级分类错误:", error);
                });
        } else {
            // 清空内容容器
            document.getElementById("contentContainer").innerHTML = `
                <div class="text-center text-muted py-3">
                    <i class="fas fa-info-circle me-2"></i>请先选择分类
                </div>
            `;
            document.getElementById("finalCategoryId").value = "";
            updateGenerateButton();
        }
    });

    // 二级分类变化事件
    document.getElementById("level2Select").addEventListener("change", function() {
        const level2Id = this.value;
        const level3Container = document.getElementById("level3Container");

        // 重置三级分类
        level3Container.style.display = "none";
        document.getElementById("level3Select").innerHTML = "<option value=\"\">请选择三级分类</option>";

        if (level2Id) {
            // 设置最终分类ID
            document.getElementById("finalCategoryId").value = level2Id;

            // 加载该分类的内容
            loadCategoryContents(level2Id);

            // 加载三级分类
            fetch(`/admin/getCategoriesByLevel?parent_id=${level2Id}`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 1 && data.data && data.data.length > 0) {
                        level3Container.style.display = "block";
                        const level3Select = document.getElementById("level3Select");
                        data.data.forEach(category => {
                            level3Select.innerHTML += `<option value="${category.id}">${category.name}</option>`;
                        });
                    }
                });
        } else {
            // 回到一级分类的内容
            const level1Id = document.getElementById("level1Select").value;
            if (level1Id) {
                document.getElementById("finalCategoryId").value = level1Id;
                loadCategoryContents(level1Id);
            }
        }
    });

    // 三级分类变化事件
    document.getElementById("level3Select").addEventListener("change", function() {
        const level3Id = this.value;

        if (level3Id) {
            // 设置最终分类ID
            document.getElementById("finalCategoryId").value = level3Id;

            // 加载该分类的内容
            loadCategoryContents(level3Id);
        } else {
            // 回到二级分类的内容
            const level2Id = document.getElementById("level2Select").value;
            if (level2Id) {
                document.getElementById("finalCategoryId").value = level2Id;
                loadCategoryContents(level2Id);
            }
        }
    });
}

// 加载分类下的内容
function loadCategoryContents(categoryId) {
    console.log("开始加载分类内容，分类ID:", categoryId);
    fetch(`/admin/getCategoryContents?category_id=${categoryId}`)
        .then(response => {
            console.log("控制台页面-API响应状态:", response.status, response.statusText);
            return response.json();
        })
        .then(data => {
            console.log("控制台页面-获取内容API响应:", data);
            const container = document.getElementById("contentContainer");

            if (data.code === 1 && data.data && data.data.length > 0) {
                let html = "<div class=\"row g-2\">";
                data.data.forEach(content => {
                    html += `
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input content-checkbox" type="checkbox"
                                       value="${content.id}" id="content_${content.id}"
                                       onchange="updateGenerateButton()">
                                <label class="form-check-label d-flex justify-content-between align-items-center w-100"
                                       for="content_${content.id}">
                                    <span>
                                        <strong>${content.title}</strong>
                                        ${content.description ? `<br><small class="text-muted">${content.description}</small>` : ""}
                                    </span>
                                    <small class="text-info">ID: ${content.id}</small>
                                </label>
                            </div>
                        </div>
                    `;
                });
                html += "</div>";
                container.innerHTML = html;
            } else {
                container.innerHTML = `
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-exclamation-circle me-2"></i>该分类下暂无内容
                    </div>
                `;
            }

            updateGenerateButton();
        })
        .catch(error => {
            console.error("加载内容失败:", error);
            document.getElementById("contentContainer").innerHTML = `
                <div class="text-center text-danger py-3">
                    <i class="fas fa-exclamation-triangle me-2"></i>加载内容失败: ${error.message}
                </div>
            `;
        });
}

// 更新生成按钮状态
function updateGenerateButton() {
    const selectedContents = document.querySelectorAll(".content-checkbox:checked");
    const generateBtn = document.getElementById("generateBtn");
    generateBtn.disabled = selectedContents.length === 0;
}

// 加载卡密设置
function loadCardSettings() {
    fetch("/admin/getCardSettings")
        .then(response => response.json())
        .then(data => {
            if (data.code === 1) {
                const settings = data.data;

                // 更新设置显示
                document.getElementById("settingCount").textContent = settings.card_generate_count || 1;
                document.getElementById("settingLength").textContent = settings.card_length || 8;
                document.getElementById("settingUsageLimit").textContent = settings.card_usage_limit || 1;
                document.getElementById("settingPrefix").textContent = settings.card_prefix || "HZO-";

                // 字符类型映射
                const charTypeMap = {
                    "mixed": "数字+字母",
                    "numbers": "纯数字",
                    "letters": "纯字母",
                    "uppercase": "大写字母",
                    "lowercase": "小写字母",
                    "alphanumeric": "字母数字"
                };
                document.getElementById("settingCharType").textContent = charTypeMap[settings.card_character_type] || "数字+字母";

                // 有效期显示
                const validityDays = settings.card_validity_days || 0;
                document.getElementById("settingValidity").textContent = validityDays > 0 ? `${validityDays}天` : "永不过期";

                // 生成预览
                generatePreview(settings);
            }
        })
        .catch(error => {
            console.error("加载卡密设置失败:", error);
        });
}

// 生成预览
function generatePreview(settings) {
    const prefix = settings.card_prefix || "";
    const length = settings.card_length || 8;
    const charType = settings.card_character_type || "mixed";

    const chars = {
        "mixed": "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",
        "numbers": "0123456789",
        "letters": "ABCDEFGHIJKLMNOPQRSTUVWXYZ",
        "uppercase": "ABCDEFGHIJKLMNOPQRSTUVWXYZ",
        "lowercase": "abcdefghijklmnopqrstuvwxyz",
        "alphanumeric": "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    };

    const charSet = chars[charType] || chars["mixed"];
    let preview = prefix;

    for (let i = 0; i < length; i++) {
        preview += charSet.charAt(Math.floor(Math.random() * charSet.length));
    }

    document.getElementById("settingPreview").textContent = preview;
}

// 处理生成卡密表单提交
document.getElementById("generateForm").addEventListener("submit", function(e) {
    e.preventDefault();

    const selectedContents = Array.from(document.querySelectorAll(".content-checkbox:checked")).map(cb => cb.value);
    const categoryId = document.getElementById("finalCategoryId").value;

    if (!categoryId) {
        AdminUtils.showMessage("请选择分类", "error");
        return;
    }

    if (selectedContents.length === 0) {
        AdminUtils.showMessage("请选择要关联的内容", "error");
        return;
    }

    const generateBtn = document.getElementById("generateBtn");
    const originalText = generateBtn.innerHTML;
    generateBtn.disabled = true;
    generateBtn.innerHTML = "<i class=\"fas fa-spinner fa-spin me-2\"></i>生成中...";

    // 发送生成请求
    const formData = new FormData();
    formData.append("category_id", categoryId);
    selectedContents.forEach(contentId => {
        formData.append("content_ids[]", contentId);
    });

    fetch("/admin/generateCards", {
        method: "POST",
        body: formData,
        headers: {
            "X-Requested-With": "XMLHttpRequest"
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            // 显示成功模态框
            showSuccessModal(data.data);

            // 关闭生成模态框
            const generateModal = bootstrap.Modal.getInstance(document.getElementById("generateModal"));
            if (generateModal) {
                generateModal.hide();
            }

            // 重置表单
            document.getElementById("generateForm").reset();
            document.getElementById("finalCategoryId").value = "";
            document.getElementById("contentContainer").innerHTML = `
                <div class="text-center text-muted py-3">
                    <i class="fas fa-info-circle me-2"></i>请先选择分类
                </div>
            `;
            document.getElementById("level2Container").style.display = "none";
            document.getElementById("level3Container").style.display = "none";
        } else {
            AdminUtils.showMessage(data.msg || "生成失败", "error");
        }
    })
    .catch(error => {
        console.error("生成卡密失败:", error);
        AdminUtils.showMessage("生成失败，请重试", "error");
    })
    .finally(() => {
        generateBtn.disabled = false;
        generateBtn.innerHTML = originalText;
        updateGenerateButton();
    });
});

// 显示成功模态框
function showSuccessModal(data) {
    const modal = new bootstrap.Modal(document.getElementById("cardSuccessModal"));

    // 构建成功内容
    let content = `
        <div class="alert alert-success">
            <h6><i class="fas fa-check-circle me-2"></i>${data.settings.card_success_title}</h6>
        </div>
        <div class="card">
            <div class="card-body">
                <pre class="mb-0" style="white-space: pre-wrap; font-family: inherit;">${formatSuccessContent(data)}</pre>
            </div>
        </div>
    `;

    document.getElementById("successContent").innerHTML = content;
    modal.show();
}

// 格式化成功内容
function formatSuccessContent(data) {
    let content = data.settings.card_success_content;

    // 替换变量
    content = content.replace(/\{CONTENT_TITLES\}/g, data.content_titles.join("、"));
    content = content.replace(/\{CARD_NUMBERS\}/g, data.cards.join("\n"));
    content = content.replace(/\{EXCHANGE_URL\}/g, data.exchange_url);

    return content;
}

// 复制成功内容
function copySuccessContent() {
    const content = document.querySelector("#successContent pre").textContent;

    if (navigator.clipboard) {
        navigator.clipboard.writeText(content).then(() => {
            showCopySuccessAndClose();
        }).catch(() => {
            fallbackCopyContent(content);
        });
    } else {
        fallbackCopyContent(content);
    }
}

// 显示复制成功提示并自动关闭
function showCopySuccessAndClose() {
    // 显示复制成功提示
    AdminUtils.showMessage("内容已复制到剪贴板，3秒后自动关闭", "success", 3000);

    // 禁用复制按钮，防止重复点击
    const copyBtn = document.querySelector("#cardSuccessModal .btn-primary");
    if (copyBtn) {
        copyBtn.disabled = true;
        copyBtn.innerHTML = "<i class=\"fas fa-check me-2\"></i>已复制";
    }

    // 3秒后关闭模态框并刷新页面
    setTimeout(() => {
        closeSuccessModal();
        // 无感刷新页面
        window.location.reload();
    }, 3000);
}

// 备用复制方法
function fallbackCopyContent(content) {
    const textArea = document.createElement("textarea");
    textArea.value = content;
    document.body.appendChild(textArea);
    textArea.select();

    try {
        document.execCommand("copy");
        showCopySuccessAndClose();
    } catch (err) {
        AdminUtils.showMessage("复制失败，请手动复制", "error");
    }

    document.body.removeChild(textArea);
}

// 关闭成功模态框
function closeSuccessModal() {
    const modal = bootstrap.Modal.getInstance(document.getElementById("cardSuccessModal"));
    if (modal) {
        modal.hide();
    }
}
</script>
';

include 'layout.php';
?>
