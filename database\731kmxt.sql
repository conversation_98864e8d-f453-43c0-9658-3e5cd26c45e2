-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.3
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2025-08-05 16:04:08
-- 服务器版本： 5.7.40
-- PHP 版本： 8.1.10

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `731kmxt`
--

-- --------------------------------------------------------

--
-- 表的结构 `account_lockouts`
--

CREATE TABLE `account_lockouts` (
  `id` int(11) NOT NULL COMMENT '锁定ID',
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'IP地址',
  `locked_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '锁定时间',
  `unlock_at` datetime NOT NULL COMMENT '解锁时间',
  `failure_count` int(11) DEFAULT '0' COMMENT '失败次数',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1锁定中，0已解锁',
  `unlock_reason` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '解锁原因'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='账户锁定记录表';

-- --------------------------------------------------------

--
-- 表的结构 `admin_users`
--

CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL COMMENT '管理员ID',
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
  `nickname` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '昵称',
  `email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号',
  `avatar` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像',
  `role` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT 'admin' COMMENT '角色',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1启用，0禁用',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最后登录IP',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员表';

--
-- 转存表中的数据 `admin_users`
--

INSERT INTO `admin_users` (`id`, `username`, `password`, `nickname`, `email`, `phone`, `avatar`, `role`, `status`, `last_login_time`, `last_login_ip`, `create_time`, `update_time`) VALUES
(1, 'admin', '$2y$10$PFaFw84c98/FoPh3EhRB.e.s0PG8Ntb2fVQMp/NHr.Zto1zJMKn1W', '红嘴鸥教育', '', '', '/uploads/avatars/avatar_1_20250804164911.png', 'admin', 1, '2025-08-05 15:44:31', '127.0.0.1', '2025-08-02 15:27:06', '2025-08-05 15:44:32');

-- --------------------------------------------------------

--
-- 表的结构 `cards`
--

CREATE TABLE `cards` (
  `id` int(11) NOT NULL COMMENT '卡密ID',
  `card_number` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '卡密号码',
  `card_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '卡密类型',
  `batch_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '批次ID',
  `category_id` int(11) DEFAULT NULL COMMENT '关联分类ID',
  `content_ids` text COLLATE utf8mb4_unicode_ci COMMENT '可兑换内容ID列表（JSON格式）',
  `value` decimal(10,2) DEFAULT '0.00' COMMENT '卡密面值',
  `valid_days` int(11) DEFAULT '0' COMMENT '有效天数，0为永久',
  `max_use_count` int(11) DEFAULT '1' COMMENT '最大使用次数',
  `used_count` int(11) DEFAULT '0' COMMENT '已使用次数',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1未使用，2已使用，0已禁用',
  `used_time` datetime DEFAULT NULL COMMENT '使用时间',
  `used_ip` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '使用IP',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='卡密表';

--
-- 转存表中的数据 `cards`
--

INSERT INTO `cards` (`id`, `card_number`, `card_type`, `batch_id`, `category_id`, `content_ids`, `value`, `valid_days`, `max_use_count`, `used_count`, `status`, `used_time`, `used_ip`, `expire_time`, `remark`, `create_time`, `update_time`) VALUES
(199, 'HZO888-K6FIF9GU', '系统生成', 'BATCH20250805120708', 30, '[\"28\"]', '0.00', 0, 1, 1, 0, '2025-08-05 12:07:31', '127.0.0.1', NULL, '根据系统设置自动生成', '2025-08-05 12:07:08', '2025-08-05 12:07:46'),
(200, 'HZO888-CTICOSNJ', '系统生成', 'BATCH20250805120840', 30, '[\"28\"]', '0.00', 0, 3, 2, 1, '2025-08-05 12:08:50', '127.0.0.1', NULL, '根据系统设置自动生成', '2025-08-05 12:08:40', '2025-08-05 12:09:26'),
(201, 'HZO888-1ZBX505U', '系统生成', 'BATCH20250805121027', 30, '[\"11\"]', '0.00', 0, 3, 0, 1, NULL, NULL, NULL, '根据系统设置自动生成', '2025-08-05 12:10:27', '2025-08-05 12:10:27'),
(202, 'HZO888-GCBP6QC9', '系统生成', 'BATCH20250805125329', 30, '[\"35\"]', '0.00', 0, 1, 0, 1, NULL, NULL, NULL, '根据系统设置自动生成', '2025-08-05 12:53:29', '2025-08-05 12:53:29'),
(203, 'HZO888-PRY8RAOE', '系统生成', 'BATCH20250805125351', 33, '[\"14\"]', '0.00', 0, 1, 1, 2, '2025-08-05 12:54:19', '127.0.0.1', NULL, '根据系统设置自动生成', '2025-08-05 12:53:51', '2025-08-05 12:54:20'),
(204, 'HZO888-RBZ4YQA2', '系统生成', 'BATCH20250805125457', 33, '[\"23\"]', '0.00', 0, 1, 0, 1, NULL, NULL, NULL, '根据系统设置自动生成', '2025-08-05 12:54:57', '2025-08-05 12:54:57'),
(205, 'HZO888-3IMC34ZH', '系统生成', 'BATCH20250805125910', 30, '[\"28\"]', '0.00', 0, 1, 0, 1, NULL, NULL, NULL, '根据系统设置自动生成', '2025-08-05 12:59:10', '2025-08-05 12:59:10'),
(206, 'HZO888-DEIKAACL', '系统生成', 'BATCH20250805125926', 30, '[\"29\"]', '0.00', 0, 1, 0, 1, NULL, NULL, NULL, '根据系统设置自动生成', '2025-08-05 12:59:26', '2025-08-05 12:59:26'),
(207, 'HZO888-CT4VVD27', '系统生成', 'BATCH20250805130249', 30, '[\"35\"]', '0.00', 0, 1, 1, 2, '2025-08-05 13:03:07', '127.0.0.1', NULL, '根据系统设置自动生成', '2025-08-05 13:02:49', '2025-08-05 13:03:07'),
(208, 'HZO888-FQT2JSTM', '系统生成', 'BATCH20250805130320', 30, '[\"34\"]', '0.00', 0, 1, 0, 1, NULL, NULL, NULL, '根据系统设置自动生成', '2025-08-05 13:03:20', '2025-08-05 13:03:20'),
(209, 'HZO888-AYEDVTEO', '系统生成', 'BATCH20250805130338', 30, '[\"28\"]', '0.00', 0, 1, 0, 1, NULL, NULL, NULL, '根据系统设置自动生成', '2025-08-05 13:03:38', '2025-08-05 13:03:38'),
(210, 'HZO888-IYBJK6I1', '系统生成', 'BATCH20250805130655', 30, '[\"35\"]', '0.00', 0, 1, 1, 2, '2025-08-05 13:14:27', '127.0.0.1', NULL, '根据系统设置自动生成', '2025-08-05 13:06:55', '2025-08-05 13:14:28'),
(211, 'HZO888-NXTP0R95', '系统生成', 'BATCH20250805141819', 30, '[\"34\"]', '0.00', 0, 1, 0, 1, NULL, NULL, NULL, '根据系统设置自动生成', '2025-08-05 14:18:19', '2025-08-05 14:18:19'),
(212, 'HZO888-3D16R1W3', '系统生成', 'BATCH20250805142029', 30, '[\"28\"]', '0.00', 0, 1, 0, 1, NULL, NULL, NULL, '根据系统设置自动生成', '2025-08-05 14:20:29', '2025-08-05 14:20:29'),
(213, 'HZO888-208W8628', '系统生成', 'BATCH20250805160043', 30, '[\"37\"]', '0.00', 0, 1, 0, 1, NULL, NULL, NULL, '根据系统设置自动生成', '2025-08-05 16:00:43', '2025-08-05 16:00:43');

-- --------------------------------------------------------

--
-- 表的结构 `categories`
--

CREATE TABLE `categories` (
  `id` int(11) NOT NULL COMMENT '分类ID',
  `parent_id` int(11) DEFAULT '0',
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '分类描述',
  `icon` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `level` int(11) DEFAULT '1',
  `path` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT '',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1启用，0禁用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='资料分类表';

--
-- 转存表中的数据 `categories`
--

INSERT INTO `categories` (`id`, `parent_id`, `name`, `description`, `icon`, `sort_order`, `level`, `path`, `status`, `create_time`, `update_time`) VALUES
(28, 0, '111一级分类', '111一级分类', '', 0, 1, NULL, 1, '2025-08-02 18:47:56', '2025-08-05 13:15:59'),
(29, 28, '111二级分类', '', '', 0, 2, ',', 1, '2025-08-02 18:48:10', '2025-08-05 13:15:59'),
(30, 29, '111三级分类', '', '', 0, 3, ',,30', 1, '2025-08-02 18:48:24', '2025-08-05 13:15:59'),
(31, 0, '222一级分类', '222一级分类', '', 0, 1, NULL, 1, '2025-08-02 18:48:38', '2025-08-04 21:15:06'),
(32, 31, '222二级分类', '', '', 0, 2, ',32', 1, '2025-08-02 18:48:47', '2025-08-03 09:06:44'),
(33, 32, '222三级分类', '', '', 0, 3, ',32,', 1, '2025-08-02 18:48:59', '2025-08-03 09:06:44'),
(34, 0, '333一级分类', '', '', 1, 1, '', 1, '2025-08-02 18:54:44', '2025-08-02 20:06:07'),
(35, 34, '333二级分类', '', '', 0, 2, ',35', 1, '2025-08-02 18:54:52', '2025-08-02 18:54:52'),
(36, 35, '333三级分类', '', '', 0, 3, ',35,36', 1, '2025-08-02 18:55:02', '2025-08-02 18:55:02'),
(37, 28, '思维导图', '', '', 0, 2, ',37', 1, '2025-08-04 21:15:50', '2025-08-05 13:15:59');

-- --------------------------------------------------------

--
-- 表的结构 `contents`
--

CREATE TABLE `contents` (
  `id` int(11) NOT NULL COMMENT '内容ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `title` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容标题',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '内容描述',
  `content` longtext COLLATE utf8mb4_unicode_ci,
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1启用，0禁用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='资料内容表';

--
-- 转存表中的数据 `contents`
--

INSERT INTO `contents` (`id`, `category_id`, `title`, `description`, `content`, `sort_order`, `status`, `create_time`, `update_time`) VALUES
(11, 30, 'MySQL数据库优化', '数据库性能优化与查询优化技巧', 'MySQL数据库优化是Web开发中的重要技能，本文档介绍了索引优化、查询优化、表结构设计等关键技术，帮助开发者提升数据库性能。', 3, 1, '2025-08-02 20:42:49', '2025-08-02 20:42:49'),
(14, 33, 'React Hooks 开发指南', 'React Hooks函数式组件开发教程', 'React Hooks是React 16.8引入的新特性，允许在函数组件中使用状态和其他React特性。本教程详细介绍各种Hook的使用方法和最佳实践。', 3, 1, '2025-08-02 20:42:49', '2025-08-02 20:42:49'),
(17, 36, 'Redis缓存应用333', 'Redis缓存技术在高并发场景下的应用', 'Redis是高性能的内存数据库，广泛应用于缓存、会话存储、消息队列等场景。本文档介绍Redis的安装配置、数据类型、持久化等核心功能。', 3, 1, '2025-08-02 20:42:49', '2025-08-03 10:15:26'),
(20, 30, 'MySQL优化指南', '数据库性能优化', 'MySQL数据库优化详细教程', 0, 1, '2025-08-02 20:43:19', '2025-08-05 13:20:21'),
(23, 33, 'React开发教程', 'React前端框架', 'React开发完整教程', 3, 1, '2025-08-02 20:43:33', '2025-08-05 13:16:11'),
(26, 36, 'Redis缓存', '缓存技术应用', 'Redis缓存技术详解', 0, 1, '2025-08-02 20:43:33', '2025-08-05 13:25:14'),
(27, 30, '测试兑换', NULL, '详细的内容，\n详细的内容\nwww.hzoedu.com\n这是下载地址www.baidu.com', 0, 1, '2025-08-03 11:58:23', '2025-08-03 12:05:06'),
(28, 30, '测试兑换02', NULL, '第一行\r\n第二行\r\n第三行\r\n下载地址www.bzidu.com', 0, 1, '2025-08-03 12:05:41', '2025-08-03 12:28:14'),
(29, 30, '测试兑换哈哈哈', NULL, '2025年二建【建筑实务】单科电子版考点精编\n\n【百度网盘下载地址】https://pan.baidu.com/s/171nrRnCX8tDcV96B5n4HMw?pwd=9bze\n【蓝奏云下载地址】https://www.ilanzou.com/s/61G0D7yq?code=8888\n\n使用方法：复制链接在手机或电脑的浏览器中打开，按照提示保存或下载即可！\n\n若链接失效请联系客服或微信hzoedu888处理！', 0, 1, '2025-08-03 12:05:41', '2025-08-05 13:16:39'),
(34, 30, '2025注安【】', NULL, '哈哈哈哈', 0, 1, '2025-08-04 21:27:17', '2025-08-04 21:27:18'),
(35, 30, '2025注安【】', NULL, '哈哈哈哈', 1, 1, '2025-08-04 21:27:18', '2025-08-05 13:16:19'),
(37, 30, '2025年一建【建筑实务】三色考点速记手册', '', '2025年一建【市政实务】一科电子版三色考点速记\n【百度网盘下载链接】https://pan.baidu.com/s/12Huuqh5yCPD1uGZwNq4CjQ?pwd=1tv1\n【蓝奏云下载链接】https://www.ilanzou.com/s/2ze0D781?code=8888\n\n【使用方法】复制链接在手机或电脑的浏览器中打开，按照提示保存或下载即可！\n【注意：若链接失效请联系客服或微信hzoedu888处理！】\n\n亲，请查收，预祝考试顺利通过！', 0, 1, '2025-08-05 16:00:04', '2025-08-05 16:00:13'),
(38, 30, '2025年一建【市政实务】三色考点速记手册', '', '2025年一建【市政实务】一科电子版三色考点速记\n【百度网盘下载链接】https://pan.baidu.com/s/12Huuqh5yCPD1uGZwNq4CjQ?pwd=1tv1\n【蓝奏云下载链接】https://www.ilanzou.com/s/2ze0D781?code=8888\n\n【使用方法】复制链接在手机或电脑的浏览器中打开，按照提示保存或下载即可！\n【注意：若链接失效请联系客服或微信hzoedu888处理！】\n\n亲，请查收，预祝考试顺利通过！', 0, 1, '2025-08-05 16:00:04', '2025-08-05 16:00:13'),
(39, 30, '2025年一建【机电实务】三色考点速记手册', '', '2025年一建【市政实务】一科电子版三色考点速记\n【百度网盘下载链接】https://pan.baidu.com/s/12Huuqh5yCPD1uGZwNq4CjQ?pwd=1tv1\n【蓝奏云下载链接】https://www.ilanzou.com/s/2ze0D781?code=8888\n\n【使用方法】复制链接在手机或电脑的浏览器中打开，按照提示保存或下载即可！\n【注意：若链接失效请联系客服或微信hzoedu888处理！】\n\n亲，请查收，预祝考试顺利通过！', 0, 1, '2025-08-05 16:00:04', '2025-08-05 16:00:15'),
(40, 30, '2025年一建【公路实务】三色考点速记手册', '', '2025年一建【市政实务】一科电子版三色考点速记\n【百度网盘下载链接】https://pan.baidu.com/s/12Huuqh5yCPD1uGZwNq4CjQ?pwd=1tv1\n【蓝奏云下载链接】https://www.ilanzou.com/s/2ze0D781?code=8888\n\n【使用方法】复制链接在手机或电脑的浏览器中打开，按照提示保存或下载即可！\n【注意：若链接失效请联系客服或微信hzoedu888处理！】\n\n亲，请查收，预祝考试顺利通过！', 0, 1, '2025-08-05 16:00:04', '2025-08-05 16:00:15'),
(41, 30, '2025年一建【水利实务】三色考点速记手册', '', '2025年一建【市政实务】一科电子版三色考点速记\n【百度网盘下载链接】https://pan.baidu.com/s/12Huuqh5yCPD1uGZwNq4CjQ?pwd=1tv1\n【蓝奏云下载链接】https://www.ilanzou.com/s/2ze0D781?code=8888\n\n【使用方法】复制链接在手机或电脑的浏览器中打开，按照提示保存或下载即可！\n【注意：若链接失效请联系客服或微信hzoedu888处理！】\n\n亲，请查收，预祝考试顺利通过！', 0, 1, '2025-08-05 16:00:04', '2025-08-05 16:00:17');

-- --------------------------------------------------------

--
-- 表的结构 `exchange_records`
--

CREATE TABLE `exchange_records` (
  `id` int(11) NOT NULL COMMENT '记录ID',
  `card_id` int(11) NOT NULL COMMENT '卡密ID',
  `card_number` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '卡密号码',
  `content_ids` text COLLATE utf8mb4_unicode_ci COMMENT '兑换的内容ID列表（JSON格式）',
  `exchange_ip` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '兑换IP',
  `user_agent` text COLLATE utf8mb4_unicode_ci COMMENT '用户代理',
  `exchange_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '兑换时间',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1成功，0失败',
  `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='兑换记录表';

--
-- 转存表中的数据 `exchange_records`
--

INSERT INTO `exchange_records` (`id`, `card_id`, `card_number`, `content_ids`, `exchange_ip`, `user_agent`, `exchange_time`, `status`, `remark`) VALUES
(1, 6, 'HZO-7PM6QY02', '\"[\\\"17\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 10:24:31', 1, '兑换成功'),
(2, 65, 'HZO-K6C4YXXP', '\"[\\\"20\\\",\\\"11\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 10:34:50', 1, '兑换成功'),
(3, 15, 'HZO-MQLY-5UP1', '\"[2,4,10,14]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 11:14:17', 1, '兑换成功'),
(4, 78, 'HZO-UWENIGDS', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 14:15:30', 1, '兑换成功'),
(5, 77, 'HZO-6E2VNL12', '\"[\\\"14\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 14:21:44', 1, '兑换成功'),
(6, 76, 'HZO-2MFKLUR3', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 14:25:36', 1, '兑换成功'),
(7, 1, 'XXXX-XXXX-XXXX-1234', '\"[11,14]\"', '127.0.0.1', NULL, '2025-08-03 14:29:03', 1, '兑换成功'),
(8, 75, 'HZO-YAPSLZ6Z', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 14:32:00', 1, '兑换成功'),
(10, 74, 'HZO-NWVIPLJJ', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 14:40:39', 1, '兑换成功'),
(12, 70, 'HZO-7UAZEPSS', '\"[\\\"28\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 14:45:07', 1, '兑换成功'),
(13, 69, 'HZO-E9JBYCHA', '\"[\\\"28\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 14:48:02', 1, '兑换成功'),
(14, 66, 'HZO-E1EBSD48', '\"[\\\"28\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 14:49:38', 1, '兑换成功'),
(15, 79, 'HZO-SP05SJZ8', '\"[\\\"28\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 14:57:50', 1, '兑换成功'),
(16, 80, 'HZO-PYE1UCJ3', '\"[\\\"28\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 14:58:05', 1, '兑换成功'),
(17, 81, 'HZO-GYH86OZ0', '\"[\\\"28\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 15:00:58', 1, '兑换成功'),
(18, 82, 'HZO-IEY9KS9M', '\"[\\\"28\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 15:05:06', 1, '兑换成功'),
(19, 83, 'HZO-SO1L2Z2Y', '\"[\\\"28\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 15:09:51', 1, '兑换成功'),
(20, 84, 'HZO-R1AXEEQZ', '\"[\\\"28\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 15:12:45', 1, '兑换成功'),
(21, 85, 'HZO-H36H1ESI', '\"[\\\"28\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 15:14:59', 1, '兑换成功'),
(22, 86, 'HZO-MYGIPH8Y', '\"[\\\"28\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 15:21:15', 1, '兑换成功'),
(23, 87, 'HZO-L516HYAN', '\"[\\\"28\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 15:29:41', 1, '兑换成功'),
(24, 88, 'HZO-SZSBO84Y', '\"[\\\"28\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 15:30:38', 1, '兑换成功'),
(25, 89, 'HZO-AJBMT8FE', '\"[\\\"28\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 15:30:58', 1, '兑换成功'),
(26, 104, 'HZO-B31W57W2', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 15:31:49', 1, '兑换成功'),
(27, 105, 'HZO-JMIBALN9', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 15:37:29', 1, '兑换成功'),
(28, 106, 'HZO-YR0PXAP2', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 15:42:46', 1, '兑换成功'),
(29, 107, 'HZO-JYZXMHJ4', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 15:46:24', 1, '兑换成功'),
(30, 108, 'HZO-Y21ULJC5', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 15:49:18', 1, '兑换成功'),
(31, 109, 'HZO-8WN2RU8E', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 15:51:10', 1, '兑换成功'),
(32, 110, 'HZO-023BKRCB', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 16:00:54', 1, '兑换成功'),
(33, 111, 'HZO-5SWH91C6', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 16:25:47', 1, '兑换成功'),
(34, 112, 'HZO-VWKRUQSS', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 16:48:08', 1, '兑换成功'),
(35, 113, 'HZO-481SMDV0', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 17:00:10', 1, '兑换成功'),
(36, 114, 'HZO-RP7S60S9', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 17:23:23', 1, '兑换成功'),
(37, 2, 'XXXX-XXXX-XXXX-5678', '\"[11,14,17,20]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.19041.6093', '2025-08-03 17:32:30', 1, '兑换成功'),
(38, 3, 'XXXX-XXXX-XXXX-9012', '\"[17,20]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.19041.6093', '2025-08-03 18:00:59', 1, '兑换成功'),
(40, 143, 'TEST-EXCHANGED-DISABLED-0003', '[1]', '127.0.0.1', 'Test Script', '2025-08-03 18:17:21', 1, NULL),
(41, 151, 'HZO-SHYIDGBW', '\"[\\\"27\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 20:54:05', 1, '兑换成功'),
(42, 152, 'HZO-V9WNNRY6', '\"[\\\"27\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-03 20:58:30', 1, '兑换成功'),
(43, 153, 'HZO-ETT97T9T', '\"[\\\"27\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-04 09:13:09', 1, '兑换成功'),
(44, 154, 'HZO-GJ2KBIL9', '\"[\\\"27\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-04 09:18:25', 1, '兑换成功'),
(45, 155, 'HZO-JTTT2JC8', '\"[\\\"27\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-04 09:20:56', 1, '兑换成功'),
(46, 156, 'HZO-4H0AZ6NO', '\"[\\\"27\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-04 09:24:23', 1, '兑换成功'),
(47, 157, 'HZO-ZQX9IMBN', '\"[\\\"27\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-04 09:24:51', 1, '兑换成功'),
(48, 158, 'HZO-T8SHRZ70', '\"[\\\"27\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-04 09:29:40', 1, '兑换成功'),
(49, 159, 'HZO-LNXXZW2D', '\"[\\\"27\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-04 09:32:34', 1, '兑换成功'),
(50, 160, 'HZO-IOFV5JVT', '\"[\\\"27\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-04 09:33:52', 1, '兑换成功'),
(51, 190, 'HZO888-CAOPPJ6L', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-04 11:41:32', 1, '兑换成功'),
(52, 189, 'HZO888-XE7EHQN7', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-04 11:56:35', 1, '兑换成功'),
(53, 191, 'HZO888-TJDA1KAU', '\"[\\\"28\\\",\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-04 13:32:58', 1, '兑换成功'),
(54, 192, 'HZO888-7C7Y1O8A', '\"[\\\"28\\\",\\\"29\\\",\\\"27\\\",\\\"20\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-04 13:34:33', 1, '兑换成功'),
(55, 169, 'HZO-2BIFD9A4', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-04 13:35:45', 1, '兑换成功'),
(56, 170, 'HZO-XGRWCI3M', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-04 13:39:27', 1, '兑换成功'),
(57, 172, 'HZO-6JMEF3QT', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-04 13:41:22', 1, '兑换成功'),
(58, 171, 'HZO-IF3USBXU', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-04 13:42:10', 1, '兑换成功'),
(59, 173, 'HZO-EYHGXD8M', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-04 13:43:13', 1, '兑换成功'),
(60, 174, 'HZO-NXY6YPN0', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-04 13:43:52', 1, '兑换成功'),
(61, 175, 'HZO-HQK0Z87E', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-04 13:48:08', 1, '兑换成功'),
(62, 176, 'HZO-Z1KMZFP8', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********', '2025-08-04 14:41:13', 1, '兑换成功'),
(63, 177, 'HZO-VP6GKR8C', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-04 14:51:02', 1, '兑换成功'),
(64, 178, 'HZO-AJPS7A74', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-04 14:58:25', 1, '兑换成功'),
(65, 179, 'HZO-7JONFNNE', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********', '2025-08-04 15:10:36', 1, '兑换成功'),
(66, 193, 'HZO888-6LOGIM5W', '\"[\\\"28\\\",\\\"29\\\",\\\"27\\\",\\\"20\\\",\\\"11\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-04 15:42:13', 1, '兑换成功'),
(67, 195, 'HZO888-DNGVWYNZ', '\"[\\\"20\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-04 21:03:26', 1, '兑换成功'),
(68, 194, 'HZO888-QXC2VSJM', '\"[\\\"29\\\",\\\"27\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-05 10:55:05', 1, '兑换成功'),
(69, 180, 'HZO-MR9CPMCY', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-05 10:59:34', 1, '兑换成功'),
(70, 181, 'HZO-KFCZARCV', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-05 11:01:22', 1, '兑换成功'),
(71, 182, 'HZO-ZRYMJVW2', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-05 11:01:57', 1, '兑换成功'),
(72, 183, 'HZO-CJ5RWAI4', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-05 11:06:07', 1, '兑换成功'),
(73, 184, 'HZO-JF15WEYD', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-05 11:49:24', 1, '兑换成功'),
(74, 195, 'HZO888-UNZEX49D', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-05 11:54:43', 1, '兑换成功'),
(75, 196, 'HZO888-EE3EHERM', '\"[\\\"20\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-05 11:59:42', 1, '兑换成功'),
(76, 196, 'HZO888-EE3EHERM', '\"[\\\"20\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-05 11:59:53', 1, '兑换成功'),
(77, 197, 'HZO888-ZMQCZK0I', '\"[\\\"35\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-05 12:01:05', 1, '兑换成功'),
(78, 197, 'HZO888-ZMQCZK0I', '\"[\\\"35\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-05 12:01:18', 1, '兑换成功'),
(79, 197, 'HZO888-ZMQCZK0I', '\"[\\\"35\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-05 12:01:25', 1, '兑换成功'),
(80, 174, 'HZO-NXY6YPN0', '\"[\\\"29\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-05 12:04:53', 1, '兑换成功'),
(81, 194, 'HZO888-QXC2VSJM', '\"[\\\"29\\\",\\\"27\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-05 12:05:29', 1, '兑换成功'),
(82, 199, 'HZO888-K6FIF9GU', '\"[\\\"28\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-05 12:07:31', 1, '兑换成功'),
(83, 200, 'HZO888-CTICOSNJ', '\"[\\\"28\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-05 12:08:50', 1, '兑换成功'),
(84, 200, 'HZO888-CTICOSNJ', '\"[\\\"28\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-05 12:09:25', 1, '兑换成功'),
(85, 203, 'HZO888-PRY8RAOE', '\"[\\\"14\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-05 12:54:19', 1, '兑换成功'),
(86, 207, 'HZO888-CT4VVD27', '\"[\\\"35\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-05 13:03:07', 1, '兑换成功'),
(87, 210, 'HZO888-IYBJK6I1', '\"[\\\"35\\\"]\"', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-05 13:14:27', 1, '兑换成功');

-- --------------------------------------------------------

--
-- 表的结构 `login_attempts`
--

CREATE TABLE `login_attempts` (
  `id` int(11) NOT NULL COMMENT '记录ID',
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'IP地址',
  `user_agent` text COLLATE utf8mb4_unicode_ci COMMENT '用户代理',
  `attempt_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '尝试时间',
  `status` tinyint(1) DEFAULT '0' COMMENT '状态：0失败，1成功',
  `failure_reason` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '失败原因'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='登录尝试记录表';

--
-- 转存表中的数据 `login_attempts`
--

INSERT INTO `login_attempts` (`id`, `username`, `ip_address`, `user_agent`, `attempt_time`, `status`, `failure_reason`) VALUES
(1, 'admin', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-04 19:07:23', 1, NULL),
(2, 'admin', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-05 10:28:18', 1, NULL),
(3, 'admin', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-05 13:54:01', 1, NULL),
(4, 'admin', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-05 15:21:56', 1, NULL),
(5, 'admin', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-08-05 15:44:31', 1, NULL);

-- --------------------------------------------------------

--
-- 表的结构 `settings`
--

CREATE TABLE `settings` (
  `id` int(11) NOT NULL COMMENT '设置ID',
  `key` varchar(100) NOT NULL COMMENT '设置键名',
  `value` text COMMENT '设置值',
  `type` varchar(20) DEFAULT 'string' COMMENT '数据类型：string,int,bool,json',
  `group` varchar(50) DEFAULT 'system' COMMENT '配置分组',
  `title` varchar(100) DEFAULT NULL COMMENT '配置标题',
  `description` varchar(500) DEFAULT NULL COMMENT '设置描述',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序权重',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统设置表';

--
-- 转存表中的数据 `settings`
--

INSERT INTO `settings` (`id`, `key`, `value`, `type`, `group`, `title`, `description`, `sort_order`, `create_time`, `update_time`) VALUES
(1, 'card_generate_count', '20', 'int', 'card', '每次生成卡密数量', '每次生成卡密数量', 0, '2025-08-03 13:15:02', '2025-08-04 09:20:16'),
(2, 'card_length', '8', 'int', 'card', '卡密长度', '卡密长度', 0, '2025-08-03 13:15:02', '2025-08-04 09:20:16'),
(3, 'card_character_type', 'mixed', 'string', 'card', '字符组合类型', '字符组合类型', 0, '2025-08-03 13:15:02', '2025-08-04 09:20:16'),
(4, 'card_usage_limit', '2', 'int', 'card', '使用次数限制', '使用次数限制', 0, '2025-08-03 13:15:02', '2025-08-04 09:20:16'),
(5, 'card_prefix', 'HZO-', 'string', 'card', '卡密前缀', '卡密前缀', 0, '2025-08-03 13:15:02', '2025-08-04 09:20:16'),
(6, 'card_suffix', '', 'string', 'card', '卡密后缀', '卡密后缀', 0, '2025-08-03 13:15:02', '2025-08-04 09:20:16'),
(7, 'card_validity_days', '0', 'int', 'card', '有效期天数', '有效期天数', 0, '2025-08-03 13:15:02', '2025-08-04 09:20:16'),
(8, 'card_separator', '', 'string', 'card', '分隔符', '分隔符', 0, '2025-08-03 13:15:02', '2025-08-04 09:20:16'),
(9, 'card_case_sensitive', '1', 'bool', 'card', '区分大小写', '区分大小写', 0, '2025-08-03 13:15:02', '2025-08-04 09:20:16'),
(10, 'card_auto_delete', '1', 'bool', 'card', '使用后自动删除', '使用后自动删除', 0, '2025-08-03 13:15:02', '2025-08-04 09:20:16'),
(11, 'card_log_usage', '1', 'bool', 'card', '记录使用日志', '记录使用日志', 0, '2025-08-03 13:15:02', '2025-08-04 09:20:16'),
(12, 'card_description', '此卡密仅限本站使用，请妥善保管。如有问题请联系客服。', 'string', 'card', '卡密说明', '卡密说明', 0, '2025-08-03 13:15:02', '2025-08-04 09:20:16'),
(13, 'site_name', '红嘴鸥教育', 'string', 'basic', '网站名称', '系统显示的网站名称', 0, '2025-08-03 13:15:02', '2025-08-04 17:56:50'),
(14, 'site_url', 'http://localhost:8000', 'string', 'basic', '网站地址', '网站地址', 0, '2025-08-03 13:15:02', '2025-08-04 17:56:50'),
(15, 'site_description', '电子资料兑换系统,电子资料兑换系统,电子资料兑换系统,电子资料兑换系统,电子资料兑换系统,', 'string', 'basic', '网站描述', '网站的描述信息', 0, '2025-08-03 13:15:02', '2025-08-04 17:56:50'),
(16, 'site_keywords', '卡密,兑换,资料,教程', 'string', 'basic', '网站关键词', '网站关键词', 0, '2025-08-03 13:15:02', '2025-08-04 17:56:50'),
(17, 'contact_email', '<EMAIL>', 'string', 'contact', '联系邮箱', '联系邮箱', 0, '2025-08-03 13:15:02', '2025-08-04 17:56:50'),
(18, 'contact_phone', '18838139634', 'string', 'contact', '联系电话', '联系电话', 0, '2025-08-03 13:15:02', '2025-08-04 17:56:50'),
(19, 'contact_qq', '895502246', 'string', 'contact', '联系QQ', '联系QQ', 0, '2025-08-03 13:15:02', '2025-08-04 17:56:50'),
(20, 'contact_wechat', 'hzoedu888', 'string', 'contact', '联系微信', '客服微信号', 0, '2025-08-03 13:15:02', '2025-08-04 17:56:50'),
(21, 'card_success_title', '卡密生成成功', 'string', 'card', '生成成功弹窗标题', '生成成功弹窗的标题', 0, '2025-08-03 13:25:02', '2025-08-04 09:20:16'),
(22, 'card_usage_instructions', '1. 复制下方的卡密号码\n2. 前往兑换页面输入卡密\n3. 点击兑换按钮获取内容\n4. 请妥善保管卡密，避免泄露', 'string', 'card', '使用方法说明', '显示在生成成功弹窗中的使用方法说明', 0, '2025-08-03 13:25:02', '2025-08-03 13:25:02'),
(23, 'card_success_notice', '• 卡密仅限一次使用，使用后自动失效\n• 请在有效期内使用，过期作废\n• 如有问题请联系客服', 'string', 'card', '温馨提示', '显示在生成成功弹窗底部的温馨提示', 0, '2025-08-03 13:25:02', '2025-08-03 13:25:02'),
(24, 'card_success_content', '{CONTENT_TITLES}\n卡密：{CARD_NUMBERS}\n兑换地址：https://kmdh.hzoedu.com\n\n使用方法：\n1. 复制卡密，点击【兑换地址】进入\n2. 输入卡密，点击【兑换按钮】\n3. 即可获取下载链接\n4. 若需再次查看下载链接，点击【兑换记录查询】\n\n（温馨提示：卡密为一人一码，兑换使用后不支持任何理由退换货）', 'string', 'card', '弹窗内容', '生成成功弹窗的完整内容模板', 0, '2025-08-03 14:02:30', '2025-08-04 09:20:16'),
(25, 'exchange_notice', '此为电子资料兑换下载系统，兑换后不支持任何理由的退换货', 'string', 'system', NULL, '用户兑换时显示的说明文字', 0, '2025-08-03 10:20:22', '2025-08-03 10:20:22'),
(26, 'max_exchange_per_day', '10', 'string', 'system', NULL, '单个IP每日最大兑换次数限制', 0, '2025-08-03 10:20:22', '2025-08-03 10:20:22'),
(27, 'file_upload_max_size', '10485760', 'string', 'system', NULL, '文件上传的最大大小（字节）', 0, '2025-08-03 10:20:22', '2025-08-03 10:20:22'),
(28, 'site_logo', '/uploads/logo_20250804141315.png', 'string', 'system', NULL, '网站Logo图片路径', 0, '2025-08-03 16:11:59', '2025-08-04 14:13:15'),
(29, 'site_title', '', 'string', 'system', NULL, '显示在浏览器标题栏的网站标题', 0, '2025-08-03 16:11:59', '2025-08-04 17:56:50'),
(30, 'exchange_success_message', '', 'string', 'system', NULL, '兑换成功时显示的提示信息', 0, '2025-08-03 16:11:59', '2025-08-04 17:56:50'),
(31, 'exchange_error_message', '', 'string', 'system', NULL, '兑换失败时显示的提示信息', 0, '2025-08-03 16:11:59', '2025-08-04 17:56:50'),
(32, 'page_footer_notice', '此为电子版资料兑换下载系统\r\n若不需要电子版资料，请勿兑换\r\n使用卡密兑换后不支持任何理由退换货\r\n有问题请联系客服或微信hzoedu888', 'string', 'system', NULL, '显示在页面底部的说明文字', 0, '2025-08-03 16:11:59', '2025-08-04 17:56:50'),
(33, 'promotion_enabled', '0', 'string', 'system', NULL, NULL, 0, '2025-08-03 16:24:55', '2025-08-04 17:56:50'),
(34, 'promotion_title', '', 'string', 'system', NULL, NULL, 0, '2025-08-03 16:24:55', '2025-08-04 17:56:50'),
(35, 'promotion_btn1_text', '', 'string', 'system', NULL, NULL, 0, '2025-08-03 16:24:55', '2025-08-04 17:56:50'),
(36, 'promotion_btn1_url', '', 'string', 'system', NULL, NULL, 0, '2025-08-03 16:24:55', '2025-08-04 17:56:50'),
(37, 'promotion_btn2_text', '', 'string', 'system', NULL, NULL, 0, '2025-08-03 16:24:55', '2025-08-04 17:56:50'),
(38, 'promotion_btn2_url', '', 'string', 'system', NULL, NULL, 0, '2025-08-03 16:24:55', '2025-08-04 17:56:50'),
(39, 'promotion_btn3_text', '', 'string', 'system', NULL, NULL, 0, '2025-08-03 16:24:55', '2025-08-04 17:56:50'),
(40, 'promotion_btn3_url', '', 'string', 'system', NULL, NULL, 0, '2025-08-03 16:24:55', '2025-08-04 17:56:50'),
(41, 'promotion_contact_text', '', 'string', 'system', NULL, NULL, 0, '2025-08-03 16:24:55', '2025-08-04 17:56:50'),
(42, 'promotion_contact_value', '', 'string', 'system', NULL, NULL, 0, '2025-08-03 16:24:55', '2025-08-04 17:56:50'),
(43, 'site_subtitle', '电子资料兑换系统', 'string', 'system', NULL, NULL, 0, '2025-08-04 12:29:13', '2025-08-04 17:56:50'),
(44, 'work_time', '周一至周五 9:00-18:00', 'string', 'system', NULL, NULL, 0, '2025-08-04 12:29:13', '2025-08-04 17:56:50'),
(45, 'contact_address', '建安区五女店镇', 'string', 'system', NULL, NULL, 0, '2025-08-04 12:29:13', '2025-08-04 17:56:50'),
(46, 'icp_number', '这是备案号显示', 'string', 'system', NULL, NULL, 0, '2025-08-04 12:29:13', '2025-08-04 17:56:50'),
(47, 'copyright', '© 2025 红嘴鸥教育', 'string', 'system', NULL, NULL, 0, '2025-08-04 12:29:13', '2025-08-04 17:56:50'),
(48, 'login_fail_limit', '5', 'string', 'system', NULL, NULL, 0, '2025-08-04 12:29:13', '2025-08-04 17:56:50'),
(49, 'lockout_duration', '30', 'string', 'system', NULL, NULL, 0, '2025-08-04 12:29:13', '2025-08-04 17:56:50'),
(50, 'session_timeout', '120', 'string', 'system', NULL, NULL, 0, '2025-08-04 12:29:13', '2025-08-04 17:56:50'),
(51, 'password_min_length', '6', 'string', 'system', NULL, NULL, 0, '2025-08-04 12:29:13', '2025-08-04 17:56:50'),
(52, 'enable_ip_whitelist', '0', 'string', 'system', NULL, NULL, 0, '2025-08-04 12:29:13', '2025-08-04 17:56:50'),
(53, 'ip_whitelist', '', 'string', 'system', NULL, NULL, 0, '2025-08-04 12:29:13', '2025-08-04 17:56:50'),
(54, 'enable_operation_log', '1', 'string', 'system', NULL, NULL, 0, '2025-08-04 12:29:13', '2025-08-04 17:56:50'),
(55, 'site_status', '1', 'string', 'system', NULL, NULL, 0, '2025-08-04 17:20:00', '2025-08-04 17:56:50');

--
-- 转储表的索引
--

--
-- 表的索引 `account_lockouts`
--
ALTER TABLE `account_lockouts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_username` (`username`),
  ADD KEY `idx_ip_address` (`ip_address`),
  ADD KEY `idx_locked_at` (`locked_at`),
  ADD KEY `idx_unlock_at` (`unlock_at`),
  ADD KEY `idx_status` (`status`);

--
-- 表的索引 `admin_users`
--
ALTER TABLE `admin_users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_username` (`username`),
  ADD KEY `idx_status` (`status`);

--
-- 表的索引 `cards`
--
ALTER TABLE `cards`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_card_number` (`card_number`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_card_type` (`card_type`),
  ADD KEY `idx_category_id` (`category_id`),
  ADD KEY `idx_expire_time` (`expire_time`);

--
-- 表的索引 `categories`
--
ALTER TABLE `categories`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_sort_order` (`sort_order`);

--
-- 表的索引 `contents`
--
ALTER TABLE `contents`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_category_id` (`category_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_sort_order` (`sort_order`);

--
-- 表的索引 `exchange_records`
--
ALTER TABLE `exchange_records`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_card_id` (`card_id`),
  ADD KEY `idx_card_number` (`card_number`),
  ADD KEY `idx_exchange_time` (`exchange_time`),
  ADD KEY `idx_status` (`status`);

--
-- 表的索引 `login_attempts`
--
ALTER TABLE `login_attempts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_username` (`username`),
  ADD KEY `idx_ip_address` (`ip_address`),
  ADD KEY `idx_attempt_time` (`attempt_time`),
  ADD KEY `idx_status` (`status`);

--
-- 表的索引 `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_key` (`key`),
  ADD KEY `idx_group` (`group`),
  ADD KEY `idx_sort_order` (`sort_order`);

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `account_lockouts`
--
ALTER TABLE `account_lockouts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '锁定ID';

--
-- 使用表AUTO_INCREMENT `admin_users`
--
ALTER TABLE `admin_users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '管理员ID', AUTO_INCREMENT=2;

--
-- 使用表AUTO_INCREMENT `cards`
--
ALTER TABLE `cards`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '卡密ID', AUTO_INCREMENT=214;

--
-- 使用表AUTO_INCREMENT `categories`
--
ALTER TABLE `categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID', AUTO_INCREMENT=38;

--
-- 使用表AUTO_INCREMENT `contents`
--
ALTER TABLE `contents`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '内容ID', AUTO_INCREMENT=42;

--
-- 使用表AUTO_INCREMENT `exchange_records`
--
ALTER TABLE `exchange_records`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID', AUTO_INCREMENT=88;

--
-- 使用表AUTO_INCREMENT `login_attempts`
--
ALTER TABLE `login_attempts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID', AUTO_INCREMENT=6;

--
-- 使用表AUTO_INCREMENT `settings`
--
ALTER TABLE `settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '设置ID', AUTO_INCREMENT=56;

--
-- 限制导出的表
--

--
-- 限制表 `contents`
--
ALTER TABLE `contents`
  ADD CONSTRAINT `fk_contents_category` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
