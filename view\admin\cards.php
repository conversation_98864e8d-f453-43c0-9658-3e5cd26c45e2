<?php
$page_title = '卡密管理';
$current_page = 'cards';
$extra_js = ['/static/js/cards.js'];

// 开始输出缓冲
ob_start();
?>
<!-- 页面头部 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-1">卡密管理</h2>
        <p class="text-muted mb-0">管理系统中的所有卡密，支持批量生成和状态管理</p>
    </div>
    <div>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#generateModal">
            <i class="fas fa-plus me-2"></i>生成卡密
        </button>
    </div>
</div>

<!-- 卡密概览 -->
<div class="stats-grid">
    <div class="stat-card primary">
        <div class="stat-header">
            <h6 class="stat-title">总卡密数量</h6>
            <div class="stat-icon primary">
                <i class="fas fa-credit-card"></i>
            </div>
        </div>
        <div class="stat-value"><?php echo number_format($overview['total_cards'] ?? 0); ?></div>
        <div class="stat-change positive">
            <i class="fas fa-arrow-up"></i>
            <span>本月新增 <?php echo $overview['month_cards'] ?? 0; ?> 张</span>
        </div>
    </div>

    <div class="stat-card success">
        <div class="stat-header">
            <h6 class="stat-title">未使用卡密</h6>
            <div class="stat-icon success">
                <i class="fas fa-clock"></i>
            </div>
        </div>
        <div class="stat-value"><?php echo number_format($overview['unused_cards'] ?? 0); ?></div>
        <div class="stat-change positive">
            <i class="fas fa-percentage"></i>
            <span>占比 <?php echo $overview['unused_rate'] ?? 0; ?>%</span>
        </div>
    </div>

    <div class="stat-card info">
        <div class="stat-header">
            <h6 class="stat-title">已使用卡密</h6>
            <div class="stat-icon info">
                <i class="fas fa-check-circle"></i>
            </div>
        </div>
        <div class="stat-value"><?php echo number_format($overview['used_cards'] ?? 0); ?></div>
        <div class="stat-change positive">
            <i class="fas fa-percentage"></i>
            <span>占比 <?php echo $overview['used_rate'] ?? 0; ?>%</span>
        </div>
    </div>

    <div class="stat-card warning">
        <div class="stat-header">
            <h6 class="stat-title">今日新增</h6>
            <div class="stat-icon warning">
                <i class="fas fa-plus-circle"></i>
            </div>
        </div>
        <div class="stat-value"><?php echo number_format($overview['today_cards'] ?? 0); ?></div>
        <div class="stat-change positive">
            <i class="fas fa-calendar-day"></i>
            <span>今日生成卡密数量</span>
        </div>
    </div>
</div>

<!-- 筛选和搜索 -->
<div class="card mb-4">
    <div class="card-body">
        <form class="row g-3 align-items-end" method="GET">
            <div class="col-md-3">
                <div class="row align-items-center g-1">
                    <div class="col-auto">
                        <label class="form-label mb-0 small">状态</label>
                    </div>
                    <div class="col">
                        <select class="form-select form-select-sm" name="status">
                            <option value="">全部</option>
                            <option value="1">未使用</option>
                            <option value="2">已使用</option>
                            <option value="0">已禁用</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="row align-items-center g-1">
                    <div class="col-auto">
                        <label class="form-label mb-0 small">搜索</label>
                    </div>
                    <div class="col">
                        <input type="text" class="form-control form-control-sm" name="search" placeholder="输入卡密号码搜索">
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary btn-sm">
                        <i class="fas fa-search me-1"></i>搜索
                    </button>
                    <a href="?" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-redo me-1"></i>重置
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 卡密列表 -->
<div class="chart-container">
    <div class="chart-header">
        <h5 class="chart-title">卡密列表</h5>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-danger btn-sm" onclick="batchDelete()">
                <i class="fas fa-trash me-1"></i>批量删除
            </button>
            <button class="btn btn-outline-secondary btn-sm" onclick="exportCards()">
                <i class="fas fa-download me-1"></i>导出
            </button>
        </div>
    </div>
    
    <div class="table-container">
        <table class="table" id="cardsTable">
            <thead>
                <tr>
                    <th width="5%" class="text-center">
                        <div class="form-check d-flex justify-content-center">
                            <input class="form-check-input" type="checkbox" id="selectAll">
                        </div>
                    </th>
                    <th width="5%" class="text-center">ID</th>
                    <th width="16%" class="text-center">卡密</th>
                    <th width="20%" class="text-center">关联内容</th>
                    <th width="8%" class="text-center">使用状态</th>
                    <th width="8%" class="text-center">启用状态</th>
                    <th width="8%" class="text-center">兑换次数</th>
                    <th width="11%" class="text-center">生成时间</th>
                    <th width="11%" class="text-center">使用时间</th>
                    <th width="8%" class="text-center">操作</th>
                </tr>
            </thead>
            <tbody>
                <?php if (isset($cards) && !empty($cards)): ?>
                    <?php foreach ($cards as $card): ?>
                    <tr data-card-id="<?php echo $card['id']; ?>">
                        <td class="text-center">
                            <div class="form-check d-flex justify-content-center">
                                <input class="form-check-input row-select" type="checkbox" value="<?php echo $card['id']; ?>">
                            </div>
                        </td>
                        <td class="text-center">
                            <span class="fw-bold"><?php echo $card['id']; ?></span>
                        </td>
                        <td class="text-center">
                            <code class="text-primary card-number-clickable"
                                  onclick="copyCardNumberFromList('<?php echo htmlspecialchars($card['card_number']); ?>')"
                                  title="点击复制卡密"
                                  style="cursor: pointer;">
                                <?php echo htmlspecialchars($card['card_number']); ?>
                            </code>
                        </td>
                        <td class="text-center">
                            <?php if (!empty($card['content_titles'])): ?>
                                <div class="d-flex flex-wrap justify-content-center gap-1">
                                    <?php foreach ($card['content_titles'] as $title): ?>
                                        <span class="badge bg-info text-dark"><?php echo htmlspecialchars($title); ?></span>
                                    <?php endforeach; ?>
                                </div>
                            <?php else: ?>
                                <span class="text-muted">无关联内容</span>
                            <?php endif; ?>
                        </td>
                        <td class="text-center">
                            <?php
                            // 使用状态：优先根据status字段判断，其次根据used_time判断
                            if ($card['status'] == 2 || !empty($card['used_time'])): ?>
                                <span class="badge bg-success">已使用</span>
                            <?php else: ?>
                                <span class="badge bg-warning text-dark">未使用</span>
                            <?php endif; ?>
                        </td>
                        <td class="text-center enable-status-cell">
                            <?php
                            // 启用状态：根据status字段判断（0=禁用，1=启用，2=已使用但仍可用）
                            if ($card['status'] == 0): ?>
                                <span class="badge bg-danger">已禁用</span>
                            <?php else: ?>
                                <span class="badge bg-primary">已启用</span>
                            <?php endif; ?>
                        </td>
                        <td class="text-center exchange-count-cell">
                            <?php
                            $usedCount = $card['used_count'] ?? 0;
                            $maxCount = $card['max_use_count'] ?? 1;

                            if ($maxCount > 1): ?>
                                <span class="badge bg-info"><?php echo $usedCount; ?>/<?php echo $maxCount; ?></span>
                            <?php else: ?>
                                <span class="badge bg-secondary"><?php echo $usedCount; ?>/<?php echo $maxCount; ?></span>
                            <?php endif; ?>
                        </td>
                        <td class="text-center">
                            <small class="text-muted"><?php echo date('Y-m-d H:i', strtotime($card['create_time'])); ?></small>
                        </td>
                        <td class="text-center">
                            <?php if ($card['used_time']): ?>
                                <small class="text-success"><?php echo date('Y-m-d H:i', strtotime($card['used_time'])); ?></small>
                            <?php else: ?>
                                <span class="text-muted">-</span>
                            <?php endif; ?>
                        </td>
                        <td class="text-center action-cell">
                            <div class="d-flex justify-content-center gap-1">
                                <button class="category-action-btn" onclick="viewCardDetails(<?php echo $card['id']; ?>)" title="详情" style="background: var(--info-light); color: var(--info-color);">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <?php if ($card['status'] == 0): ?>
                                <button class="category-action-btn toggle-btn" onclick="toggleCardStatus(<?php echo $card['id']; ?>, 1)" title="启用" style="background: var(--success-light); color: var(--success-color);">
                                    <i class="fas fa-toggle-on"></i>
                                </button>
                                <?php else: ?>
                                <button class="category-action-btn toggle-btn" onclick="toggleCardStatus(<?php echo $card['id']; ?>, 0)" title="禁用" style="background: var(--warning-light); color: var(--warning-color);">
                                    <i class="fas fa-toggle-off"></i>
                                </button>
                                <?php endif; ?>
                                <button class="category-action-btn delete-btn" onclick="deleteCard(<?php echo $card['id']; ?>)" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="9" class="text-center py-4">
                            <div class="text-muted">
                                <i class="fas fa-inbox fa-2x mb-2"></i>
                                <p class="mb-0">暂无卡密数据</p>
                            </div>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
    
    <!-- 分页 -->
    <div class="pagination-container mt-3">
        <div class="pagination-info">
            <?php if (isset($cards) && method_exists($cards, 'render')): ?>
                <span class="text-muted">
                    共 <?php echo $cards->total(); ?> 条记录，
                    当前第 <?php echo $cards->currentPage(); ?> 页，
                    共 <?php echo $cards->lastPage(); ?> 页
                </span>
                <div class="page-size-selector">
                    <span class="page-size-label">每页显示</span>
                    <select class="page-size-select" onchange="changePageSize(this.value)">
                        <option value="10" <?php echo (isset($search_params['page_size']) && $search_params['page_size'] == 10) ? 'selected' : ''; ?>>10</option>
                        <option value="15" <?php echo (!isset($search_params['page_size']) || $search_params['page_size'] == 15) ? 'selected' : ''; ?>>15</option>
                        <option value="20" <?php echo (isset($search_params['page_size']) && $search_params['page_size'] == 20) ? 'selected' : ''; ?>>20</option>
                        <option value="50" <?php echo (isset($search_params['page_size']) && $search_params['page_size'] == 50) ? 'selected' : ''; ?>>50</option>
                        <option value="100" <?php echo (isset($search_params['page_size']) && $search_params['page_size'] == 100) ? 'selected' : ''; ?>>100</option>
                    </select>
                    <span class="page-size-unit">条</span>
                </div>
            <?php else: ?>
                <span class="text-muted">暂无数据</span>
                <div class="page-size-selector">
                    <span class="page-size-label">每页显示</span>
                    <select class="page-size-select" onchange="changePageSize(this.value)">
                        <option value="10">10</option>
                        <option value="15" selected>15</option>
                        <option value="20">20</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                    <span class="page-size-unit">条</span>
                </div>
            <?php endif; ?>
        </div>

        <div class="pagination-nav">
            <?php if (isset($pagination_html)): ?>
                <?php echo $pagination_html; ?>
            <?php else: ?>
                <!-- 默认分页 -->
                <nav aria-label="分页导航">
                    <ul class="custom-pagination">
                        <li class="custom-page-item disabled">
                            <span class="custom-page-link disabled">上一页</span>
                        </li>
                        <li class="custom-page-item active">
                            <span class="custom-page-link active">1</span>
                        </li>
                        <li class="custom-page-item disabled">
                            <span class="custom-page-link disabled">下一页</span>
                        </li>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- 生成卡密模态框 -->
<div class="modal fade" id="generateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus-circle me-2"></i>生成卡密
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="generateForm">
                <div class="modal-body">
                    <div class="row g-4">
                        <!-- 分级分类选择 -->
                        <div class="col-12">
                            <label class="form-label">
                                <i class="fas fa-folder me-2"></i>选择分类
                            </label>

                            <!-- 一级分类选择 -->
                            <div class="mb-3">
                                <select class="form-select" id="level1Select" required>
                                    <option value="">请选择一级分类</option>
                                </select>
                            </div>

                            <!-- 二级分类选择 -->
                            <div class="mb-3" id="level2Container" style="display: none;">
                                <select class="form-select" id="level2Select">
                                    <option value="">请选择二级分类</option>
                                </select>
                            </div>

                            <!-- 三级分类选择 -->
                            <div class="mb-3" id="level3Container" style="display: none;">
                                <select class="form-select" id="level3Select">
                                    <option value="">请选择三级分类</option>
                                </select>
                            </div>

                            <!-- 隐藏字段存储最终选择的分类ID -->
                            <input type="hidden" name="category_id" id="finalCategoryId">
                        </div>

                        <!-- 第二行：选择内容 -->
                        <div class="col-12">
                            <label class="form-label">
                                <i class="fas fa-file-alt me-2"></i>选择该分类下的内容
                            </label>
                            <div id="contentContainer" class="border rounded p-3 text-start" style="min-height: 120px; max-height: 200px; overflow-y: auto;">
                                <div class="text-center text-muted py-3">
                                    <i class="fas fa-info-circle me-2"></i>请先选择分类
                                </div>
                            </div>
                        </div>

                        <!-- 卡密设置信息显示 -->
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title mb-3">
                                        <i class="fas fa-cog me-2"></i>当前卡密生成设置
                                    </h6>
                                    <div class="row g-2 small">
                                        <div class="col-md-3">
                                            <strong>生成数量：</strong><span id="settingCount">1</span>个
                                        </div>
                                        <div class="col-md-3">
                                            <strong>卡密长度：</strong><span id="settingLength">8</span>位
                                        </div>
                                        <div class="col-md-3">
                                            <strong>字符组合：</strong><span id="settingCharType">数字+字母</span>
                                        </div>
                                        <div class="col-md-3">
                                            <strong>使用次数：</strong><span id="settingUsageLimit">1</span>次
                                        </div>
                                        <div class="col-md-3">
                                            <strong>卡密前缀：</strong><span id="settingPrefix">HZO-</span>
                                        </div>
                                        <div class="col-md-3">
                                            <strong>有效期：</strong><span id="settingValidity">永不过期</span>
                                        </div>
                                        <div class="col-md-6">
                                            <strong>预览格式：</strong><code id="settingPreview">HZO-A1B2C3D4</code>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>取消
                    </button>
                    <button type="submit" class="btn btn-primary" id="generateBtn" disabled>
                        <i class="fas fa-magic me-2"></i>生成卡密
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 卡密详情模态框 -->
<div class="modal fade" id="cardDetailsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">卡密详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="card">
                    <div class="card-body">
                        <!-- 卡密号码 -->
                        <div class="mb-3 pb-2 border-bottom card-detail-item">
                            <div class="row align-items-center">
                                <div class="col-3 card-detail-label">
                                    <strong>卡密号码：</strong>
                                </div>
                                <div class="col-9 card-detail-value">
                                    <span id="detail-card-number" class="card-number-clickable" onclick="copyCardNumber()" title="点击复制卡密" style="cursor: pointer;"></span>
                                </div>
                            </div>
                        </div>

                        <!-- 内容标题 -->
                        <div class="mb-3 pb-2 border-bottom card-detail-item">
                            <div class="row">
                                <div class="col-3 card-detail-label">
                                    <strong>内容标题：</strong>
                                </div>
                                <div class="col-9 card-detail-value" id="detail-content-list">
                                    <!-- 关联内容列表将在这里显示 -->
                                </div>
                            </div>
                        </div>

                        <!-- 使用状态 -->
                        <div class="mb-3 pb-2 border-bottom card-detail-item">
                            <div class="row align-items-center">
                                <div class="col-3 card-detail-label">
                                    <strong>使用状态：</strong>
                                </div>
                                <div class="col-9 card-detail-value">
                                    <span id="detail-use-status"></span>
                                </div>
                            </div>
                        </div>

                        <!-- 生成时间 -->
                        <div class="mb-3 pb-2 border-bottom card-detail-item">
                            <div class="row align-items-center">
                                <div class="col-3 card-detail-label">
                                    <strong>生成时间：</strong>
                                </div>
                                <div class="col-9 card-detail-value">
                                    <span id="detail-create-time" class="text-muted"></span>
                                </div>
                            </div>
                        </div>

                        <!-- 使用时间 -->
                        <div class="mb-3 pb-2 border-bottom card-detail-item">
                            <div class="row align-items-center">
                                <div class="col-3 card-detail-label">
                                    <strong>使用时间：</strong>
                                </div>
                                <div class="col-9 card-detail-value">
                                    <span id="detail-used-time" class="text-muted"></span>
                                </div>
                            </div>
                        </div>

                        <!-- 使用者IP -->
                        <div class="mb-3 pb-2 border-bottom card-detail-item">
                            <div class="row align-items-center">
                                <div class="col-3 card-detail-label">
                                    <strong>使用者IP：</strong>
                                </div>
                                <div class="col-9 card-detail-value">
                                    <span id="detail-used-ip" class="text-muted"></span>
                                </div>
                            </div>
                        </div>

                        <!-- 过期时间 -->
                        <div class="mb-3 card-detail-item">
                            <div class="row align-items-center">
                                <div class="col-3 card-detail-label">
                                    <strong>过期时间：</strong>
                                </div>
                                <div class="col-9 card-detail-value">
                                    <span id="detail-expire-time" class="text-muted"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 卡密生成成功弹窗 -->
<div class="modal fade" id="cardSuccessModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle me-2"></i>
                    <span id="success-modal-title">卡密生成成功</span>
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="success-content-display" class="text-start" style="font-family: 'Courier New', monospace; line-height: 1.6; padding: 20px;">
                    <!-- 简化的内容将在这里显示 -->
                </div>
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-primary" onclick="copyAllContent()">
                    <i class="fas fa-copy me-2"></i>复制内容
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// HTML转义函数，防止XSS攻击
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 全选功能
document.getElementById("selectAll").addEventListener("change", function() {
    const checkboxes = document.querySelectorAll(".row-select");
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// 分级分类选择逻辑
let currentCategoryId = null;

// 初始化分类选择
function initCategorySelects() {
    loadLevel1Categories();

    // 一级分类选择事件
    document.getElementById("level1Select").addEventListener("change", function() {
        const categoryId = this.value;
        resetLowerLevels(2);

        if (categoryId) {
            // 检查是否有二级分类
            checkAndLoadChildren(categoryId, 2);
        } else {
            clearContent();
        }
    });

    // 二级分类选择事件
    document.getElementById("level2Select").addEventListener("change", function() {
        const categoryId = this.value;
        resetLowerLevels(3);

        if (categoryId) {
            // 检查是否有三级分类
            checkAndLoadChildren(categoryId, 3);
        } else {
            clearContent();
        }
    });

    // 三级分类选择事件
    document.getElementById("level3Select").addEventListener("change", function() {
        const categoryId = this.value;

        if (categoryId) {
            // 三级分类直接加载内容
            loadCategoryContent(categoryId);
        } else {
            clearContent();
        }
    });
}

// 加载一级分类
function loadLevel1Categories() {
    fetch('/admin/getCategoriesByLevel?level=1')
        .then(response => response.json())
        .then(data => {
            console.log("卡密管理页面-一级分类API响应:", data);
            if (data.code === 1) {
                const select = document.getElementById("level1Select");
                select.innerHTML = '<option value="">请选择一级分类</option>';

                data.data.forEach(category => {
                    select.innerHTML += `<option value="${category.id}">${escapeHtml(category.name)}</option>`;
                });
            } else {
                console.error("获取一级分类失败:", data);
            }
        })
        .catch(error => {
            console.error("加载一级分类失败:", error);
        });
}

// 检查并加载子分类
function checkAndLoadChildren(categoryId, targetLevel) {
    fetch(`/admin/checkCategoryChildren?category_id=${categoryId}`)
        .then(response => response.json())
        .then(data => {
            if (data.code === 1) {
                if (data.data.has_children) {
                    // 有子分类，加载子分类选择框
                    loadChildCategories(categoryId, targetLevel);
                } else {
                    // 没有子分类，这是最深层级，加载内容
                    loadCategoryContent(categoryId);
                }
            }
        })
        .catch(error => {
            console.error("检查子分类失败:", error);
            // 出错时也加载内容
            loadCategoryContent(categoryId);
        });
}

// 加载子分类
function loadChildCategories(parentId, level) {
    fetch(`/admin/getCategoriesByLevel?parent_id=${parentId}`)
        .then(response => response.json())
        .then(data => {
            if (data.code === 1) {
                const containerId = `level${level}Container`;
                const selectId = `level${level}Select`;
                const container = document.getElementById(containerId);
                const select = document.getElementById(selectId);

                if (data.data.length > 0) {
                    // 显示选择框并填充选项
                    container.style.display = 'block';
                    select.innerHTML = `<option value="">请选择${level === 2 ? '二' : '三'}级分类</option>`;

                    data.data.forEach(category => {
                        select.innerHTML += `<option value="${category.id}">${escapeHtml(category.name)}</option>`;
                    });

                    // 不自动加载内容，等待用户选择
                } else {
                    // 没有子分类，说明当前分类就是最深层级，加载内容
                    loadCategoryContent(parentId);
                }
            } else {
                // API调用失败，加载父分类内容
                loadCategoryContent(parentId);
            }
        })
        .catch(error => {
            console.error("加载子分类失败:", error);
            // 出错时加载父分类内容
            loadCategoryContent(parentId);
        });
}

// 重置低级别的选择框
function resetLowerLevels(fromLevel) {
    for (let i = fromLevel; i <= 3; i++) {
        const container = document.getElementById(`level${i}Container`);
        const select = document.getElementById(`level${i}Select`);

        container.style.display = 'none';
        select.innerHTML = `<option value="">请选择${i === 2 ? '二' : '三'}级分类</option>`;
    }
    // 重置时清空内容显示
    clearContent();
}

// 加载分类内容
function loadCategoryContent(categoryId) {
    currentCategoryId = categoryId;
    document.getElementById("finalCategoryId").value = categoryId;

    const contentContainer = document.getElementById("contentContainer");
    const generateBtn = document.getElementById("generateBtn");

    // 显示加载状态
    contentContainer.innerHTML = '<div class="text-center py-3"><i class="fas fa-spinner fa-spin me-2"></i>加载中...</div>';

    // 获取分类下的内容
    console.log("开始加载分类内容，分类ID:", categoryId);
    fetch(`/admin/getCategoryContents?category_id=${categoryId}`)
        .then(response => {
            console.log("API响应状态:", response.status, response.statusText);
            return response.json();
        })
        .then(data => {
            console.log("卡密管理页面-分类内容API响应:", data);
            if (data.code === 1 && data.data.length > 0) {
                let html = '<div class="d-flex flex-column align-items-start w-100">';
                data.data.forEach(content => {
                    html += `
                        <div class="form-check mb-2 text-start w-100">
                            <input class="form-check-input content-checkbox" type="checkbox" value="${content.id}" name="content_ids[]" id="content_${content.id}">
                            <label class="form-check-label text-start w-100" for="content_${content.id}" style="text-align: left !important;">
                                ${escapeHtml(content.title)}
                            </label>
                        </div>
                    `;
                });
                html += '</div>';
                contentContainer.innerHTML = html;

                // 监听内容选择变化
                document.querySelectorAll(".content-checkbox").forEach(checkbox => {
                    checkbox.addEventListener("change", updateGenerateButton);
                });

                updateGenerateButton();
            } else {
                contentContainer.innerHTML = '<div class="text-center text-muted py-3"><i class="fas fa-exclamation-circle me-2"></i>该分类下暂无内容</div>';
                generateBtn.disabled = true;
            }
        })
        .catch(error => {
            contentContainer.innerHTML = '<div class="text-center text-danger py-3"><i class="fas fa-exclamation-triangle me-2"></i>加载失败，请重试</div>';
            generateBtn.disabled = true;
        });
}

// 清空内容显示
function clearContent() {
    currentCategoryId = null;
    document.getElementById("finalCategoryId").value = '';
    document.getElementById("contentContainer").innerHTML = '<div class="text-center text-muted py-3"><i class="fas fa-info-circle me-2"></i>请先选择分类</div>';
    document.getElementById("generateBtn").disabled = true;
}

// 更新生成按钮状态
function updateGenerateButton() {
    const selectedContents = document.querySelectorAll(".content-checkbox:checked");
    const generateBtn = document.getElementById("generateBtn");
    generateBtn.disabled = selectedContents.length === 0;
}

// 加载卡密设置
function loadCardSettings() {
    fetch("/admin/getCardSettings")
        .then(response => response.json())
        .then(data => {
            if (data.code === 1) {
                const settings = data.data;
                document.getElementById("settingCount").textContent = settings.card_generate_count || 1;
                document.getElementById("settingLength").textContent = settings.card_length || 8;
                document.getElementById("settingCharType").textContent = getCharTypeText(settings.card_character_type || "mixed");
                document.getElementById("settingUsageLimit").textContent = settings.card_usage_limit || 1;
                document.getElementById("settingPrefix").textContent = settings.card_prefix || "HZO-";
                document.getElementById("settingValidity").textContent = settings.card_validity_days == 0 ? "永不过期" : settings.card_validity_days + "天";
                document.getElementById("settingPreview").textContent = generatePreview(settings);
            }
        })
        .catch(error => {
            console.error("获取卡密设置失败:", error);
        });
}

// 获取字符类型文本
function getCharTypeText(type) {
    const typeMap = {
        "mixed": "数字+字母",
        "numbers": "仅数字",
        "letters": "仅字母",
        "uppercase": "仅大写字母",
        "lowercase": "仅小写字母",
        "alphanumeric": "数字+大写字母"
    };
    return typeMap[type] || "数字+字母";
}

// 生成预览
function generatePreview(settings) {
    const prefix = settings.card_prefix || "";
    const length = settings.card_length || 8;
    const charType = settings.card_character_type || "mixed";

    const chars = {
        "mixed": "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",
        "numbers": "0123456789",
        "letters": "ABCDEFGHIJKLMNOPQRSTUVWXYZ",
        "uppercase": "ABCDEFGHIJKLMNOPQRSTUVWXYZ",
        "lowercase": "abcdefghijklmnopqrstuvwxyz",
        "alphanumeric": "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    };

    const charSet = chars[charType] || chars["mixed"];
    let sample = "";
    for (let i = 0; i < length; i++) {
        sample += charSet[Math.floor(Math.random() * charSet.length)];
    }

    return prefix + sample;
}

// 生成卡密
document.getElementById("generateForm").addEventListener("submit", function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());

    // 处理多选框
    const contentIds = formData.getAll("content_ids[]");
    data.content_ids = contentIds;

    if (contentIds.length === 0) {
        AdminUtils.showMessage("请选择要关联的内容", "warning");
        return;
    }

    // 发送AJAX请求到后端
    AdminUtils.ajax({
        url: "/admin/generateCards",
        method: "POST",
        data: data
    }).then(response => {
        if (response.code === 1) {
            // 隐藏生成模态框
            bootstrap.Modal.getInstance(document.getElementById("generateModal")).hide();

            // 显示成功弹窗
            showCardSuccessModal(response.data);
        } else {
            AdminUtils.showMessage(response.msg, "error");
        }
    }).catch(error => {
        AdminUtils.showMessage("生成失败：" + error.message, "error");
    });
});

// 显示卡密生成成功弹窗
function showCardSuccessModal(data) {
    // 设置弹窗标题
    document.getElementById('success-modal-title').textContent = data.settings.card_success_title || '卡密生成成功';

    // 生成内容
    const contentDisplay = document.getElementById('success-content-display');

    // 获取弹窗内容模板
    let contentTemplate = data.settings.card_success_content || "内容标题：{CONTENT_TITLES}\n卡密：{CARD_NUMBERS}\n兑换地址：https://kmdh.hzoedu.com\n\n使用方法：\n1. 复制卡密，点击兑换地址进入\n2. 输入卡密，点击兑换按钮\n3. 即可获取下载链接\n4. 若需再次查看下载链接，点击【兑换记录查询】\n\n（温馨提示：卡密为一人一码，兑换使用后不支持任何理由退换货）";

    // 准备替换变量
    // 标题（关联内容）
    let contentTitles = '';
    if (data.content_titles && data.content_titles.length > 0) {
        contentTitles = data.content_titles.join('、');
    } else {
        contentTitles = '暂无关联内容';
    }

    // 卡密
    let cardNumbers = '';
    if (data.cards && data.cards.length > 0) {
        cardNumbers = data.cards.join('\n');
    } else {
        cardNumbers = '暂无卡密';
    }

    // 卡密数量
    const cardCount = data.cards ? data.cards.length : 0;

    // 批次ID
    const batchId = data.batch_id || '';

    // 兑换地址
    const exchangeUrl = data.exchange_url || window.location.origin + window.location.pathname.replace('/admin/cards', '');

    // 替换模板中的所有变量
    let finalContent = contentTemplate
        .replace(/{CONTENT_TITLES}/g, contentTitles)
        .replace(/{CARD_NUMBERS}/g, cardNumbers)
        .replace(/{CARD_COUNT}/g, cardCount)
        .replace(/{BATCH_ID}/g, batchId)
        .replace(/{EXCHANGE_URL}/g, exchangeUrl)
        // 兼容旧版本的变量名
        .replace(/内容跟标题/g, contentTitles)
        .replace(/变量/g, cardNumbers);

    // 生成HTML内容
    const htmlContent = `<div style="white-space: pre-line;">${finalContent}</div>`;

    contentDisplay.innerHTML = htmlContent;

    // 保存完整内容用于复制
    window.cardSuccessContent = finalContent;

    // 显示弹窗
    const modal = new bootstrap.Modal(document.getElementById('cardSuccessModal'));
    modal.show();
}



// 复制所有内容（增强版本）
function copyAllContent() {
    const content = window.cardSuccessContent || '';

    if (!content) {
        AdminUtils.showMessage("没有可复制的内容", "error");
        return;
    }

    // 检查是否为安全上下文
    const isSecureContext = window.isSecureContext || location.protocol === 'https:' || location.hostname === 'localhost' || location.hostname === '127.0.0.1';

    // 优先使用现代剪贴板API（仅在安全上下文中）
    if (isSecureContext && navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(content).then(() => {
            console.log('使用 navigator.clipboard 复制成功');
            showCopySuccessAndClose();
        }).catch((err) => {
            console.warn('navigator.clipboard 复制失败，降级到传统方法:', err);
            fallbackCopyContent(content);
        });
    } else {
        console.log('使用传统复制方法');
        fallbackCopyContent(content);
    }
}

// 显示复制成功提示并自动关闭
function showCopySuccessAndClose() {
    // 显示复制成功提示
    AdminUtils.showMessage("内容已复制到剪贴板，3秒后自动关闭", "success", 3000);

    // 禁用复制按钮，防止重复点击
    const copyBtn = document.querySelector("#cardSuccessModal .btn-primary");
    if (copyBtn) {
        copyBtn.disabled = true;
        copyBtn.innerHTML = '<i class="fas fa-check me-2"></i>已复制';
    }

    // 3秒后关闭模态框并刷新页面
    setTimeout(() => {
        const modal = bootstrap.Modal.getInstance(document.getElementById('cardSuccessModal'));
        if (modal) {
            modal.hide();
        }
        // 无感刷新页面
        window.location.reload();
    }, 3000);
}

// 兼容性复制内容函数（增强版本）
function fallbackCopyContent(text) {
    try {
        // 方法1：使用 textarea 元素
        const textArea = document.createElement("textarea");
        textArea.value = text;
        textArea.style.top = "0";
        textArea.style.left = "0";
        textArea.style.position = "fixed";
        textArea.style.opacity = "0";
        textArea.style.pointerEvents = "none";
        textArea.setAttribute('readonly', '');
        textArea.setAttribute('contenteditable', 'true');

        document.body.appendChild(textArea);

        // 尝试选择文本
        textArea.focus();
        textArea.select();
        textArea.setSelectionRange(0, text.length);

        // 尝试复制
        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);

        if (successful) {
            console.log('使用 document.execCommand 复制成功');
            showCopySuccessAndClose();
        } else {
            throw new Error('document.execCommand 返回 false');
        }
    } catch (err) {
        console.error('传统复制方法失败:', err);

        // 方法2：尝试使用 Range 和 Selection
        try {
            const range = document.createRange();
            const selection = window.getSelection();
            const div = document.createElement('div');
            div.textContent = text;
            div.style.position = 'fixed';
            div.style.left = '-9999px';
            document.body.appendChild(div);

            range.selectNode(div);
            selection.removeAllRanges();
            selection.addRange(range);

            const successful = document.execCommand('copy');
            document.body.removeChild(div);
            selection.removeAllRanges();

            if (successful) {
                console.log('使用 Range/Selection 复制成功');
                showCopySuccessAndClose();
            } else {
                throw new Error('Range/Selection 方法失败');
            }
        } catch (err2) {
            console.error('所有复制方法都失败了:', err2);
            showManualCopyDialog(text);
        }
    }
}

// 显示手动复制对话框
function showManualCopyDialog(text) {
    // 创建模态框HTML
    const modalHtml = `
        <div class="modal fade" id="manualCopyModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">手动复制内容</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p class="text-muted mb-3">自动复制失败，请手动选择并复制以下内容：</p>
                        <textarea class="form-control" rows="10" readonly style="font-family: monospace;">${text}</textarea>
                        <div class="mt-3">
                            <small class="text-info">
                                <i class="fas fa-info-circle me-1"></i>
                                提示：点击文本框，按 Ctrl+A 全选，然后按 Ctrl+C 复制
                            </small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        <button type="button" class="btn btn-primary" onclick="selectAllText()">全选文本</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('manualCopyModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新模态框
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('manualCopyModal'));
    modal.show();

    // 模态框关闭时清理
    document.getElementById('manualCopyModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// 全选文本框内容
function selectAllText() {
    const textarea = document.querySelector('#manualCopyModal textarea');
    if (textarea) {
        textarea.focus();
        textarea.select();
        textarea.setSelectionRange(0, textarea.value.length);
        AdminUtils.showMessage("文本已选中，请按 Ctrl+C 复制", "info", 3000);
    }
}

// 关闭成功弹窗（不刷新页面）
function closeSuccessModal() {
    const modal = bootstrap.Modal.getInstance(document.getElementById('cardSuccessModal'));
    if (modal) {
        modal.hide();
    }
}

// 模态框显示时加载设置
document.getElementById("generateModal").addEventListener("show.bs.modal", function() {
    loadCardSettings();
    initCategorySelects();
});

// 改变每页显示数量
function changePageSize(pageSize) {
    const url = new URL(window.location);
    url.searchParams.set("page_size", pageSize);
    url.searchParams.set("page", 1); // 重置到第一页
    window.location.href = url.toString();
}

// 更新卡密行的状态显示
function updateCardRowStatus(id, status) {
    const row = document.querySelector(`tr[data-card-id="${id}"]`);
    if (!row) return;

    // 更新启用状态列
    const statusCell = row.querySelector('.enable-status-cell');
    if (statusCell) {
        if (status == 0) {
            statusCell.innerHTML = '<span class="badge bg-danger">已禁用</span>';
        } else {
            statusCell.innerHTML = '<span class="badge bg-primary">已启用</span>';
        }
    }

    // 更新操作按钮
    const actionCell = row.querySelector('.action-cell');
    if (actionCell) {
        const toggleBtn = actionCell.querySelector('.toggle-btn');
        if (toggleBtn) {
            if (status == 0) {
                toggleBtn.setAttribute('onclick', `toggleCardStatus(${id}, 1)`);
                toggleBtn.setAttribute('title', '启用');
                toggleBtn.style.background = 'var(--success-light)';
                toggleBtn.style.color = 'var(--success-color)';
                toggleBtn.innerHTML = '<i class="fas fa-toggle-on"></i>';
            } else {
                toggleBtn.setAttribute('onclick', `toggleCardStatus(${id}, 0)`);
                toggleBtn.setAttribute('title', '禁用');
                toggleBtn.style.background = 'var(--warning-light)';
                toggleBtn.style.color = 'var(--warning-color)';
                toggleBtn.innerHTML = '<i class="fas fa-toggle-off"></i>';
            }
        }
    }
}

// 切换卡密状态（按钮调用）
function toggleCardStatus(id, status) {
    if (window.AdminUtils) {
        AdminUtils.confirm(`确定要${status ? '启用' : '禁用'}这个卡密吗？`, function() {
            performToggleCardStatus(id, status);
        });
    } else {
        if (confirm(`确定要${status ? '启用' : '禁用'}这个卡密吗？`)) {
            performToggleCardStatus(id, status);
        }
    }
}

// 执行状态切换
function performToggleCardStatus(id, status) {
    fetch("/admin/toggleCardStatus", {
        method: "POST",
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
        },
        body: `id=${encodeURIComponent(id)}&status=${encodeURIComponent(status)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            // 成功时显示提示并更新界面
            if (window.AdminUtils) {
                AdminUtils.showMessage(status ? "卡密已启用" : "卡密已禁用", "success", 2000);
            }
            // 无感更新界面
            updateCardRowStatus(id, status);
        } else {
            AdminUtils.showMessage(data.msg || "操作失败", "error");
        }
    })
    .catch(error => {
        console.error("状态更新错误:", error);
        AdminUtils.showMessage("网络错误", "error");
    });
}

// 查看卡密详情
function viewCardDetails(id) {
    fetch(`/admin/getCardDetails?id=${id}`)
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            const card = data.data;

            // 填充卡密号码
            document.getElementById('detail-card-number').textContent = card.card_number;

            // 填充使用状态
            const useStatus = (card.status == 2 || card.used_time) ?
                '<span class="badge bg-success">已使用</span>' :
                '<span class="badge bg-warning text-dark">未使用</span>';
            document.getElementById('detail-use-status').innerHTML = useStatus;

            // 填充时间信息
            document.getElementById('detail-create-time').textContent = formatDateTime(card.create_time);
            document.getElementById('detail-used-time').textContent = card.used_time ? formatDateTime(card.used_time) : '-';
            document.getElementById('detail-used-ip').textContent = card.used_ip || '-';
            // 处理过期时间显示
            const expireTimeElement = document.getElementById('detail-expire-time');
            if (!card.expire_time || card.expire_time === '0000-00-00 00:00:00' || card.expire_time === null) {
                expireTimeElement.textContent = '永久有效';
                expireTimeElement.className = 'text-success fw-bold';
            } else {
                const expireDate = new Date(card.expire_time);
                const now = new Date();

                if (expireDate > now) {
                    expireTimeElement.textContent = formatDateTime(card.expire_time);
                    expireTimeElement.className = 'text-muted';
                } else {
                    expireTimeElement.textContent = formatDateTime(card.expire_time) + ' (已过期)';
                    expireTimeElement.className = 'text-danger';
                }
            }

            // 填充关联内容
            const contentList = document.getElementById('detail-content-list');
            if (card.content_titles && card.content_titles.length > 0) {
                let contentHtml = '';
                card.content_titles.forEach((title, index) => {
                    if (index > 0) contentHtml += '、';
                    contentHtml += `<span class="content-title">${escapeHtml(title)}</span>`;
                });
                contentList.innerHTML = contentHtml;
            } else {
                contentList.innerHTML = '<span class="text-muted">暂无关联内容</span>';
            }

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('cardDetailsModal'));
            modal.show();
        } else {
            AdminUtils.showMessage(data.msg, "error");
        }
    })
    .catch(error => {
        console.error("获取卡密详情错误:", error);
        AdminUtils.showMessage("获取卡密详情失败：网络错误", "error");
    });
}

// 复制卡密号码（详情模态框中）
function copyCardNumber() {
    const cardNumber = document.getElementById('detail-card-number').textContent;
    copyToClipboard(cardNumber);
}

// 复制卡密号码（列表中）
function copyCardNumberFromList(cardNumber) {
    copyToClipboard(cardNumber);
}

// 通用复制函数（增强版本）
function copyToClipboard(text) {
    if (!text) {
        AdminUtils.showMessage("没有可复制的内容", "error");
        return;
    }

    // 检查是否为安全上下文
    const isSecureContext = window.isSecureContext || location.protocol === 'https:' || location.hostname === 'localhost' || location.hostname === '127.0.0.1';

    // 优先使用现代剪贴板API（仅在安全上下文中）
    if (isSecureContext && navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(text).then(() => {
            console.log('卡密复制成功 (navigator.clipboard)');
            AdminUtils.showMessage("卡密已复制到剪贴板", "success", 2000);
        }).catch((err) => {
            console.warn('navigator.clipboard 复制失败，降级到传统方法:', err);
            fallbackCopyTextToClipboard(text);
        });
    } else {
        console.log('使用传统方法复制卡密');
        fallbackCopyTextToClipboard(text);
    }
}

// 兼容性复制函数（增强版本）
function fallbackCopyTextToClipboard(text) {
    try {
        const textArea = document.createElement("textarea");
        textArea.value = text;
        textArea.style.top = "0";
        textArea.style.left = "0";
        textArea.style.position = "fixed";
        textArea.style.opacity = "0";
        textArea.style.pointerEvents = "none";
        textArea.setAttribute('readonly', '');

        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        textArea.setSelectionRange(0, text.length);

        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);

        if (successful) {
            console.log('卡密复制成功 (document.execCommand)');
            AdminUtils.showMessage("卡密已复制到剪贴板", "success", 2000);
        } else {
            throw new Error('document.execCommand 返回 false');
        }
    } catch (err) {
        console.error('传统复制方法失败:', err);
        AdminUtils.showMessage("复制失败，卡密内容：" + text, "error", 5000);
    }
}

// 格式化日期时间
function formatDateTime(dateTimeString) {
    if (!dateTimeString) return '-';

    const date = new Date(dateTimeString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

// 删除卡密
function deleteCard(id) {
    AdminUtils.confirm("确定要删除这个卡密吗？删除后无法恢复！", function() {
        performDeleteCard(id);
    });
}

// 执行删除卡密
function performDeleteCard(id) {
    fetch("/admin/deleteCard", {
        method: "POST",
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
        },
        body: `id=${encodeURIComponent(id)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            // 成功时显示提示并移除行
            AdminUtils.showMessage("卡密删除成功", "success", 2000);
            // 无感移除行
            removeCardRow(id);
        } else {
            AdminUtils.showMessage(data.msg || "删除失败", "error");
        }
    })
    .catch(error => {
        console.error("删除错误:", error);
        AdminUtils.showMessage("网络错误", "error");
    });
}

// 移除卡密行
function removeCardRow(id) {
    const row = document.querySelector(`tr[data-card-id="${id}"]`);
    if (row) {
        // 添加淡出动画
        row.style.transition = 'opacity 0.3s ease';
        row.style.opacity = '0';

        // 动画完成后移除行
        setTimeout(() => {
            row.remove();
            // 检查是否还有数据，如果没有显示空状态
            checkEmptyState();
        }, 300);
    }
}

// 检查空状态
function checkEmptyState() {
    const tbody = document.querySelector('#cardsTable tbody');
    const dataRows = tbody.querySelectorAll('tr[data-card-id]');

    if (dataRows.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="9" class="text-center py-4">
                    <div class="text-muted">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <p class="mb-0">暂无卡密数据</p>
                    </div>
                </td>
            </tr>
        `;
    }
}

// 批量删除
function batchDelete() {
    const selected = document.querySelectorAll(".row-select:checked");
    if (selected.length === 0) {
        AdminUtils.showMessage("请选择要删除的卡密", "warning");
        return;
    }

    AdminUtils.confirm(`确定要删除选中的 ${selected.length} 个卡密吗？删除后无法恢复！`, function() {
        performBatchDeleteCards(selected);
    });
}

// 执行批量删除卡密
function performBatchDeleteCards(selectedElements) {
    // 获取选中的卡密ID
    const ids = Array.from(selectedElements).map(checkbox => {
        const row = checkbox.closest('tr');
        return row.getAttribute('data-card-id');
    }).filter(id => id); // 过滤空值

    if (ids.length === 0) {
        AdminUtils.showMessage("未找到有效的卡密ID", "error");
        return;
    }

    fetch("/admin/batchDeleteCards", {
        method: "POST",
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
        },
        body: `ids=${encodeURIComponent(ids.join(','))}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            // 成功时显示提示并移除行
            AdminUtils.showMessage(data.msg, "success", 3000);

            // 移除成功删除的行
            ids.forEach(id => {
                removeCardRow(id);
            });

            // 取消全选状态
            const selectAllCheckbox = document.querySelector("#selectAll");
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = false;
            }
        } else {
            AdminUtils.showMessage(data.msg || "批量删除失败", "error");
        }
    })
    .catch(error => {
        AdminUtils.showMessage("网络错误", "error");
    });
}

// 导出卡密
function exportCards() {
    // 获取当前的筛选条件
    const urlParams = new URLSearchParams(window.location.search);
    const status = urlParams.get('status') || '';
    const search = urlParams.get('search') || '';

    // 构建导出URL
    let exportUrl = '/admin/exportCards';
    const params = [];

    if (status !== '') {
        params.push('status=' + encodeURIComponent(status));
    }
    if (search !== '') {
        params.push('search=' + encodeURIComponent(search));
    }

    if (params.length > 0) {
        exportUrl += '?' + params.join('&');
    }

    // 显示导出提示
    AdminUtils.showMessage("正在准备导出文件，请稍候...", "info", 2000);

    try {
        // 创建隐藏的下载链接并触发下载
        const link = document.createElement('a');
        link.href = exportUrl;
        link.download = '';
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // 延迟显示完成提示
        setTimeout(() => {
            AdminUtils.showMessage("导出完成！如果没有开始下载，请检查浏览器的下载设置。", "success", 4000);
        }, 1500);

    } catch (error) {
        console.error('导出错误:', error);
        AdminUtils.showMessage("导出失败，请重试", "error");
    }
}

// 初始化表格
document.addEventListener("DOMContentLoaded", function() {
    TableManager.init("cardsTable", { sortable: true });
});
</script>

<?php
// 获取输出缓冲的内容并赋值给$content变量
$content = ob_get_clean();

// 包含布局文件
include 'layout.php';
?>
