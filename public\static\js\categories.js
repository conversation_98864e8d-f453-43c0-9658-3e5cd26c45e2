// 分类管理JavaScript
let currentEditId = null;
let selectedCategoryId = null;
let selectedForDelete = [];

// HTML转义函数，防止XSS攻击
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 保存展开状态到localStorage
function saveExpandState() {
    const expandedCategories = [];
    document.querySelectorAll('.category-toggle i.fa-chevron-down').forEach(icon => {
        const row = icon.closest('tr');
        if (row) {
            expandedCategories.push(row.dataset.id);
        }
    });
    localStorage.setItem('categoryExpandState', JSON.stringify(expandedCategories));

    // 保存选中状态
    if (selectedCategoryId) {
        localStorage.setItem('selectedCategoryId', selectedCategoryId);
    }
}

// 恢复展开状态
function restoreExpandState() {
    const savedState = localStorage.getItem('categoryExpandState');
    if (savedState) {
        try {
            const expandedCategories = JSON.parse(savedState);
            expandedCategories.forEach(categoryId => {
                const row = document.querySelector(`[data-id="${categoryId}"]`);
                if (row) {
                    const toggle = row.querySelector('.category-toggle i');
                    const children = document.querySelectorAll(`[data-parent="${categoryId}"]`);

                    if (children.length > 0 && toggle) {
                        showChildren(categoryId);
                        toggle.className = "fas fa-chevron-down";
                    }
                }
            });
        } catch (e) {
            // 恢复展开状态失败，忽略错误
        }
    }

    // 恢复选中状态
    const savedSelectedId = localStorage.getItem('selectedCategoryId');
    if (savedSelectedId) {
        updateSelectedCategory(savedSelectedId);
    }
}

// 重新加载分类列表（无感刷新）
function reloadCategoryList() {
    fetch('/admin/categories')
        .then(response => response.text())
        .then(html => {
            // 创建临时DOM来解析返回的HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = html;

            // 提取新的表格内容
            const newTableBody = tempDiv.querySelector('.category-tree');
            const currentTableBody = document.querySelector('.category-tree');

            if (newTableBody && currentTableBody) {
                // 替换表格内容
                currentTableBody.innerHTML = newTableBody.innerHTML;

                // 重新初始化事件和状态
                initializeCategoryTree();

                // 恢复展开状态
                setTimeout(() => {
                    restoreExpandState();
                }, 100);
            }
        })
        .catch(error => {
            console.error('重新加载分类列表失败:', error);
            // 如果无感刷新失败，则使用传统刷新
            window.location.reload();
        });
}

// 加载分类树（用于上级分类选择）
function loadCategoryTree(excludeId = 0, callback = null) {
    const url = excludeId > 0 ? `/admin/getCategoryTree?exclude_id=${excludeId}` : '/admin/getCategoryTree';

    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.code === 1) {
                const select = document.getElementById('category_parent');
                select.innerHTML = '<option value="0">无上级分类（顶级分类）</option>';

                // 递归构建选项
                function buildOptions(categories, prefix = '') {
                    categories.forEach(category => {
                        const option = document.createElement('option');
                        option.value = category.id;
                        option.textContent = prefix + category.name;

                        // 检查层级限制（最多3级）
                        if (category.level < 2) { // 0,1,2 对应 1,2,3级
                            select.appendChild(option);
                        }

                        // 递归处理子分类
                        if (category.children && category.children.length > 0) {
                            buildOptions(category.children, prefix + '　├─ ');
                        }
                    });
                }

                buildOptions(data.data);

                if (callback) {
                    callback();
                }
            } else {
                console.error('加载分类树失败:', data.msg);
                if (callback) {
                    callback();
                }
            }
        })
        .catch(error => {
            console.error('加载分类树错误:', error);
            if (callback) {
                callback();
            }
        });
}

// 更新选中的分类
function updateSelectedCategory(id) {
    // 移除之前的选中状态
    document.querySelectorAll('.category-name.selected').forEach(name => {
        name.classList.remove('selected');
    });

    // 添加新的选中状态
    const currentRow = document.querySelector(`[data-id="${id}"]`);
    if (currentRow) {
        const categoryName = currentRow.querySelector('.category-name');
        if (categoryName) {
            categoryName.classList.add('selected');
            selectedCategoryId = id;
        }
    }
}

// 全选/取消全选
function toggleSelectAll(checkbox) {
    const categoryCheckboxes = document.querySelectorAll('.category-checkbox');
    categoryCheckboxes.forEach(cb => {
        cb.checked = checkbox.checked;
        updateRowSelection(cb);
    });
    updateBatchDeleteButton();
}

// 更新批量删除按钮显示状态
function updateBatchDeleteButton() {
    const checkedBoxes = document.querySelectorAll('.category-checkbox:checked');
    const batchDeleteBtn = document.getElementById('batchDeleteBtn');
    const selectAllCheckbox = document.getElementById('selectAll');

    if (checkedBoxes.length > 0) {
        batchDeleteBtn.style.display = 'block';
        batchDeleteBtn.textContent = `批量删除 (${checkedBoxes.length})`;
    } else {
        batchDeleteBtn.style.display = 'none';
    }

    // 更新全选复选框状态
    const allCheckboxes = document.querySelectorAll('.category-checkbox');
    if (checkedBoxes.length === allCheckboxes.length && allCheckboxes.length > 0) {
        selectAllCheckbox.checked = true;
        selectAllCheckbox.indeterminate = false;
    } else if (checkedBoxes.length > 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = true;
    } else {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    }

    // 更新行的选中样式
    allCheckboxes.forEach(checkbox => {
        updateRowSelection(checkbox);
    });
}

// 更新行的选中样式
function updateRowSelection(checkbox) {
    const row = checkbox.closest('tr');
    if (checkbox.checked) {
        row.classList.add('selected-for-delete');
    } else {
        row.classList.remove('selected-for-delete');
    }
}

// 更新分类排序
function updateCategorySort(id, sortOrder) {
    const input = document.querySelector(`input[data-id="${id}"]`);
    const originalValue = input.dataset.original;

    // 如果值没有变化，不发送请求
    if (sortOrder == originalValue) {
        return;
    }

    // 验证输入值
    if (!sortOrder || isNaN(sortOrder) || sortOrder < 0) {
        AdminUtils.showMessage("请输入有效的排序值（大于等于0的数字）", "warning", 3000);
        input.value = originalValue;
        return;
    }

    // 添加加载状态
    input.classList.add('loading');
    input.disabled = true;

    fetch('/admin/updateCategorySort', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `id=${encodeURIComponent(id)}&sort_order=${encodeURIComponent(sortOrder)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            if (window.AdminUtils) {
                AdminUtils.showMessage(data.msg || "排序更新成功", "success", 2000);
            }

            // 更新原始值
            input.dataset.original = sortOrder;

            // 如果排序值有实际变化，进行无感刷新
            if (data.data && data.data.old_sort != data.data.new_sort) {
                setTimeout(() => {
                    reloadCategoryList();
                }, 500);
            }
        } else {
            AdminUtils.showMessage(data.msg || "排序更新失败", "error", 3000);
            // 恢复原始值
            input.value = originalValue;
        }
    })
    .catch(error => {
        console.error('更新排序错误:', error);
        AdminUtils.showMessage("网络错误，请稍后重试", "error", 3000);
        // 恢复原始值
        input.value = originalValue;
    })
    .finally(() => {
        input.classList.remove('loading');
        input.disabled = false;
    });
}



// 批量删除分类
function batchDeleteCategories() {
    const checkedBoxes = document.querySelectorAll('.category-checkbox:checked');
    if (checkedBoxes.length === 0) {
        AdminUtils.showMessage("请选择要删除的分类", "warning", 3000);
        return;
    }

    const categoryIds = Array.from(checkedBoxes).map(cb => cb.value);
    const categoryNames = Array.from(checkedBoxes).map(cb => {
        const row = cb.closest('tr');
        return row.querySelector('.category-name').textContent;
    });

    if (confirm(`确定要删除以下 ${categoryIds.length} 个分类吗？\n\n${categoryNames.join('\n')}\n\n删除后无法恢复！`)) {
        // 禁用按钮防止重复点击
        const batchDeleteBtn = document.getElementById('batchDeleteBtn');
        const originalText = batchDeleteBtn.innerHTML;
        batchDeleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>删除中...';
        batchDeleteBtn.disabled = true;

        fetch('/admin/batchDeleteCategories', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                ids: categoryIds
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 1) {
                AdminUtils.showMessage(data.msg || "批量删除成功", "success", 2000);

                // 移除已删除的行
                categoryIds.forEach(id => {
                    const row = document.querySelector(`[data-id="${id}"]`);
                    if (row) {
                        row.remove();
                    }
                });

                // 重置选择状态
                document.getElementById('selectAll').checked = false;
                updateBatchDeleteButton();
                saveExpandState();
            } else {
                AdminUtils.showMessage(data.msg || "批量删除失败", "error", 3000);
            }
        })
        .catch(error => {
            console.error('批量删除错误:', error);
            AdminUtils.showMessage("网络错误，请稍后重试", "error", 3000);
        })
        .finally(() => {
            batchDeleteBtn.innerHTML = originalText;
            batchDeleteBtn.disabled = false;
        });
    }
}

// 切换分类展开/收起（同级分类互斥）
function toggleCategory(id) {
    const currentRow = document.querySelector(`[data-id="${id}"]`);
    if (!currentRow) {
        console.error('找不到ID为', id, '的分类行');
        return;
    }

    // 更新选中状态
    updateSelectedCategory(id);

    const children = document.querySelectorAll(`[data-parent="${id}"]`);
    const toggle = currentRow.querySelector('.category-toggle i');

    if (children.length === 0) return;

    const isExpanded = toggle && toggle.classList.contains("fa-chevron-down");

    if (isExpanded) {
        // 收起当前分类
        hideChildren(id);
        if (toggle) {
            toggle.className = "fas fa-chevron-right";
        }
    } else {
        // 先收起所有同级已展开的分类
        const currentParentId = currentRow.dataset.parent;
        const allRows = document.querySelectorAll('.category-item');

        allRows.forEach(row => {
            const rowId = row.dataset.id;
            const rowParentId = row.dataset.parent;

            // 如果是同级分类且不是当前分类
            if (rowParentId === currentParentId && rowId !== id) {
                const rowToggle = row.querySelector('.category-toggle i');
                if (rowToggle && rowToggle.classList.contains("fa-chevron-down")) {
                    hideChildren(rowId);
                    rowToggle.className = "fas fa-chevron-right";
                }
            }
        });

        // 展开当前分类
        showChildren(id);
        if (toggle) {
            toggle.className = "fas fa-chevron-down";
        }
    }

    // 保存展开状态
    saveExpandState();
}

// 显示子分类
function showChildren(parentId) {
    const children = document.querySelectorAll(`[data-parent="${parentId}"]`);
    children.forEach(child => {
        child.style.display = "table-row";
        child.classList.remove("collapsed");
    });
}

// 隐藏子分类及其所有后代
function hideChildren(parentId) {
    const children = document.querySelectorAll(`[data-parent="${parentId}"]`);
    children.forEach(child => {
        child.style.display = "none";
        child.classList.add("collapsed");

        // 递归隐藏子级的子级
        const childId = child.dataset.id;
        hideChildren(childId);

        // 重置子级的展开状态
        const childToggle = child.querySelector('.category-toggle i');
        if (childToggle) {
            childToggle.className = "fas fa-chevron-right";
        }
    });
}



// 展开全部
function expandAll() {
    document.querySelectorAll(".category-item").forEach(row => {
        const id = row.dataset.id;
        const hasChildren = document.querySelectorAll(`[data-parent="${id}"]`).length > 0;
        if (hasChildren) {
            showChildren(id);
            const toggle = row.querySelector('.category-toggle i');
            if (toggle) {
                toggle.className = "fas fa-chevron-down";
            }
        }
    });
}

// 收起全部
function collapseAll() {
    document.querySelectorAll(".category-item").forEach(row => {
        const id = row.dataset.id;
        const hasChildren = document.querySelectorAll(`[data-parent="${id}"]`).length > 0;
        if (hasChildren) {
            hideChildren(id);
            const toggle = row.querySelector('.category-toggle i');
            if (toggle) {
                toggle.className = "fas fa-chevron-right";
            }
        }
    });
}

// 切换分类状态
function toggleCategoryStatus(id, switchElement) {
    const status = switchElement.checked ? 1 : 0;
    const statusText = status ? "启用" : "禁用";
    
    // 显示加载状态
    switchElement.disabled = true;
    
    // 发送API请求
    fetch('/admin/updateCategoryStatus', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `id=${id}&status=${status}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            if (window.AdminUtils) {
                AdminUtils.showMessage(data.msg || ("分类状态已" + statusText), "success", 2000);
            }

            // 级联操作需要刷新列表以显示效果
            setTimeout(() => {
                reloadCategoryList();
            }, 1000);
        } else {
            AdminUtils.showMessage(data.msg, "error", 3000);
            // 恢复开关状态
            switchElement.checked = !switchElement.checked;
        }
    })
    .catch(error => {
        AdminUtils.showMessage("网络错误，请稍后重试", "error", 3000);
        // 恢复开关状态
        switchElement.checked = !switchElement.checked;
    })
    .finally(() => {
        switchElement.disabled = false;
    });
}

// 添加分类
function addCategory() {
    currentEditId = null;
    document.getElementById("categoryForm").reset();
    document.getElementById("categoryModalLabel").textContent = "添加分类";

    // 加载分类树
    loadCategoryTree(0, () => {
        new bootstrap.Modal(document.getElementById("categoryModal")).show();
    });
}

// 添加子分类
function addSubCategory(parentId) {
    currentEditId = null;
    document.getElementById("categoryForm").reset();
    document.getElementById("categoryModalLabel").textContent = "添加子分类";

    // 加载分类树并设置默认父分类
    loadCategoryTree(0, () => {
        document.getElementById("category_parent").value = parentId;
        new bootstrap.Modal(document.getElementById("categoryModal")).show();
    });
}

// 编辑分类
function editCategory(id) {
    currentEditId = id;
    document.getElementById("categoryModalLabel").textContent = "编辑分类";

    // 先加载分类树（排除当前分类及其子级）
    loadCategoryTree(id, () => {
        // 然后获取分类数据
        fetch(`/admin/getCategory?id=${id}`)
            .then(response => response.json())
            .then(data => {
                if (data.code === 1) {
                    const category = data.data;
                    document.getElementById("category_name").value = category.name || '';
                    document.getElementById("category_description").value = category.description || '';
                    document.getElementById("category_sort").value = category.sort_order || 0;
                    document.getElementById("category_status").value = category.status || 1;
                    document.getElementById("category_parent").value = category.parent_id || 0;

                    new bootstrap.Modal(document.getElementById("categoryModal")).show();
                } else {
                    AdminUtils.showMessage(data.msg || "获取分类信息失败", "error", 3000);
                }
            })
            .catch(error => {
                console.error('获取分类信息错误:', error);
                AdminUtils.showMessage("网络错误，请稍后重试", "error", 3000);
            });
    });
}

// 删除分类
function deleteCategory(id) {
    if (!id) {
        console.error('删除分类：ID为空');
        AdminUtils.showMessage("分类ID无效", "error", 3000);
        return;
    }

    if (confirm("确定要删除这个分类吗？删除后无法恢复！")) {
        fetch('/admin/deleteCategory', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `id=${encodeURIComponent(id)}`
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.code === 1) {
                AdminUtils.showMessage(data.msg || "删除成功", "success", 2000);
                // 动态移除DOM元素
                const rowToDelete = document.querySelector(`[data-id="${id}"]`);
                if (rowToDelete) {
                    rowToDelete.remove();
                }
                // 更新保存的状态
                saveExpandState();
            } else {
                AdminUtils.showMessage(data.msg || "删除失败", "error", 3000);
            }
        })
        .catch(error => {
            AdminUtils.showMessage("网络错误，请稍后重试", "error", 3000);
        });
    }
}

// 表单提交
document.addEventListener('DOMContentLoaded', function() {
    const categoryForm = document.getElementById("categoryForm");
    if (categoryForm) {
        categoryForm.addEventListener("submit", function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            if (currentEditId) {
                formData.append('id', currentEditId);
            }
            
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = '保存中...';
            submitBtn.disabled = true;
            
            fetch('/admin/saveCategory', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 1) {
                    AdminUtils.showMessage(data.msg, "success", 2000);
                    bootstrap.Modal.getInstance(document.getElementById("categoryModal")).hide();
                    // 无感刷新分类列表
                    setTimeout(() => {
                        reloadCategoryList();
                    }, 500);
                } else {
                    AdminUtils.showMessage(data.msg, "error", 3000);
                }
            })
            .catch(error => {
                AdminUtils.showMessage("网络错误，请稍后重试", "error", 3000);
            })
            .finally(() => {
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            });
        });
    }
    
    // 初始化分类状态
    initializeCategoryTree();

    // 恢复展开状态
    setTimeout(() => {
        restoreExpandState();
    }, 100);

    // 初始化批量删除按钮状态
    updateBatchDeleteButton();
});

// 初始化分类树
function initializeCategoryTree() {
    // 默认收起所有子分类
    document.querySelectorAll(".category-children").forEach(children => {
        children.classList.add("collapsed");
        children.style.display = "none";
    });
    
    // 设置正确的图标状态
    document.querySelectorAll(".category-toggle i").forEach(icon => {
        const row = icon.closest("tr");
        const id = row.dataset.id;
        const hasChildren = document.querySelectorAll(`[data-parent="${id}"]`).length > 0;
        
        if (hasChildren) {
            icon.className = "fas fa-chevron-right";
        } else {
            // 没有子分类的项目隐藏切换按钮
            const toggleBtn = icon.closest(".category-toggle");
            if (toggleBtn) {
                toggleBtn.style.visibility = "hidden";
            }
        }
    });
    
    // 初始化状态开关和禁用样式
    document.querySelectorAll(".category-item").forEach(row => {
        const status = row.dataset.status;
        const switchElement = row.querySelector(".category-status-switch");
        
        if (switchElement) {
            switchElement.checked = status === "1";
        }
        
        if (status === "0") {
            row.classList.add("category-disabled");
        }
    });
}
